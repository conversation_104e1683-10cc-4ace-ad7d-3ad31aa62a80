import os
import logging
from logging.handlers import RotatingFileHandler
import click
from app import create_app
from app.models import User, Strategy, ScheduledPost, AutoReplySettings, Log, LogLevel, LogCategory
import colorama
from colorama import Fore, Style
from mongoengine import connect, disconnect
from pymongo import MongoClient
import subprocess
import sys

# Initialize colorama for Windows support
colorama.init()

def setup_logging(app):
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = RotatingFileHandler('logs/rominext.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter('%(message)s'))
    
    app.logger.addHandler(file_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('Rominext startup')

def check_mongodb():
    try:
        client = MongoClient('mongodb://localhost:27017/')
        client.server_info()
        click.echo(f"{Fore.GREEN}✓ MongoDB connection successful{Style.RESET_ALL}")
        return True
    except Exception as e:
        click.echo(f"{Fore.RED}✗ MongoDB connection failed: {str(e)}{Style.RESET_ALL}")
        return False

def check_environment():
    required_vars = ['FACEBOOK_PAGE_ACCESS_TOKEN', 'FACEBOOK_PAGE_ID', 'GROQ_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        click.echo(f"{Fore.RED}✗ Missing environment variables: {', '.join(missing_vars)}{Style.RESET_ALL}")
        return False
    click.echo(f"{Fore.GREEN}✓ Environment variables verified{Style.RESET_ALL}")
    return True

def check_port_in_use(port=5000):
    """Check if the port is in use and kill the process if needed."""
    try:
        # Check if port is in use
        result = subprocess.run(['lsof', '-i', f':{port}'], 
                               capture_output=True, text=True)
        
        if result.stdout:
            # Extract PIDs
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            pids = [line.split()[1] for line in lines]
            
            click.echo(f"{Fore.YELLOW}Port {port} is in use. Killing processes...{Style.RESET_ALL}")
            
            # Kill each process
            for pid in pids:
                try:
                    subprocess.run(['kill', '-9', pid])
                    click.echo(f"{Fore.GREEN}Killed process {pid}{Style.RESET_ALL}")
                except Exception as e:
                    click.echo(f"{Fore.RED}Failed to kill process {pid}: {str(e)}{Style.RESET_ALL}")
            
            return True
        return False
    except Exception as e:
        click.echo(f"{Fore.RED}Error checking port: {str(e)}{Style.RESET_ALL}")
        return False

@click.group()
def cli():
    """Rominext Management CLI"""
    pass

@cli.command()
@click.option('--debug', is_flag=True, help='Enable debug mode')
@click.option('--port', default=5000, help='Port to run the server on')
@click.option('--auto-kill', is_flag=True, help='Automatically kill processes using the port')
def run(debug, port, auto_kill):
    """Run the Rominext application"""
    click.echo(f"{Fore.CYAN}Starting Rominext...{Style.RESET_ALL}")
    
    # Check if port is in use and kill if requested
    if auto_kill:
        check_port_in_use(port)
    
    # Startup checks
    click.echo("\nPerforming startup checks:")
    checks_passed = True
    
    if not check_mongodb():
        checks_passed = False
    if not check_environment():
        checks_passed = False
    
    if not checks_passed:
        click.echo(f"\n{Fore.RED}Startup checks failed. Please fix the issues and try again.{Style.RESET_ALL}")
        return
    
    click.echo(f"\n{Fore.GREEN}All checks passed! Starting server...{Style.RESET_ALL}\n")
    
    try:
        # Clean any existing connections
        disconnect()
        
        # Create and configure the application
        app = create_app()
        
        # Enable Swagger regardless of debug mode if requested
        from flask_swagger_ui import get_swaggerui_blueprint
        from app.config import get_settings
        
        settings = get_settings()
        settings.ENABLE_SWAGGER = True
        
        # Register Swagger UI blueprint
        SWAGGER_URL = '/api/docs'
        API_URL = '/static/swagger.json'
        swaggerui_blueprint = get_swaggerui_blueprint(
            SWAGGER_URL,
            API_URL,
            config={'app_name': "Rominext API"}
        )
        app.register_blueprint(swaggerui_blueprint, url_prefix=SWAGGER_URL)
        click.echo(f"{Fore.YELLOW}Swagger UI enabled at http://localhost:{port}{SWAGGER_URL}{Style.RESET_ALL}")
        
        setup_logging(app)
        
        click.echo(f"{Fore.YELLOW}Server running on http://localhost:{port}{Style.RESET_ALL}")
        
        # When in debug mode with auto-kill, use use_reloader=False to prevent the reloader
        # from triggering the auto-kill in a loop
        if debug and auto_kill:
            app.run(debug=debug, port=port, use_reloader=False)
        else:
            app.run(debug=debug, port=port)
    except Exception as e:
        click.echo(f"{Fore.RED}Error starting server: {str(e)}{Style.RESET_ALL}")
        raise
    finally:
        disconnect()

@cli.command()
def init_db():
    """Initialize the database"""
    click.echo(f"{Fore.CYAN}Initializing database...{Style.RESET_ALL}")
    
    if not check_mongodb():
        return
    
    try:
        # Ensure clean connection state
        disconnect()
        
        app = create_app()
        with app.app_context():
            # Clear collections in reverse dependency order
            AutoReplySettings.objects.delete()
            ScheduledPost.objects.delete()
            # Remove PageProfile.objects.delete() line
            Strategy.objects.delete()
            User.objects.delete()
            
            click.echo(f"{Fore.GREEN}✓ Database initialized successfully{Style.RESET_ALL}")
    except Exception as e:
        click.echo(f"{Fore.RED}✗ Database initialization failed: {str(e)}{Style.RESET_ALL}")
        raise  # Add raise to see full traceback during development
    finally:
        disconnect()

@cli.command()
@click.option('--email', prompt=True, help='Admin user email')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='Admin user password')
def create_admin(email, password):
    """Create an admin user"""
    if not check_mongodb():
        return
        
    try:
        disconnect()
        app = create_app()
        
        with app.app_context():
            if User.objects(email=email).first():
                click.echo(f"{Fore.RED}✗ User with email {email} already exists{Style.RESET_ALL}")
                return
            
            # Use the create_user static method
            User.create_user(
                email=email,
                password=password,
                name='Admin',
                is_active=True
            )
            
            click.echo(f"{Fore.GREEN}✓ Admin user created successfully{Style.RESET_ALL}")
    except Exception as e:
        click.echo(f"{Fore.RED}✗ Failed to create admin user: {str(e)}{Style.RESET_ALL}")
    finally:
        disconnect()

@cli.command()
def health_check():
    """Check system health"""
    click.echo(f"{Fore.CYAN}Performing health check...{Style.RESET_ALL}\n")
    
    checks = [
        ("MongoDB Connection", check_mongodb),
        ("Environment Variables", check_environment),
    ]
    
    all_passed = True
    for name, check in checks:
        click.echo(f"Checking {name}...")
        try:
            result = check()
            if not result:
                all_passed = False
        except Exception as e:
            click.echo(f"{Fore.RED}✗ {name} check failed: {str(e)}{Style.RESET_ALL}")
            all_passed = False
    
    if all_passed:
        click.echo(f"\n{Fore.GREEN}All health checks passed!{Style.RESET_ALL}")
    else:
        click.echo(f"\n{Fore.RED}Some health checks failed.{Style.RESET_ALL}")

if __name__ == '__main__':
    cli()



