# Rominext - AI-Powered Social Media Management Platform

![Rominext Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=Rominext)

## 🚀 Overview

Rominext is a comprehensive, AI-powered social media management platform designed to streamline content creation, scheduling, and analytics across multiple social media platforms. Built with Flask and modern web technologies, it provides businesses and content creators with powerful tools to manage their social media presence effectively.

## ✨ Key Features

### 🤖 AI-Powered Content Generation
- **Multi-Provider AI Integration**: Support for OpenAI, DeepSeek, Grok, HuggingFace, TogetherAI, StabilityAI, and DeepInfra
- **Intelligent Content Creation**: Generate captions, posts, and responses tailored to your brand voice
- **Smart Content Optimization**: AI-driven suggestions for better engagement

### 📱 Multi-Platform Support
- **Facebook**: Page management, posting, and analytics
- **Instagram**: Content publishing and engagement tracking
- **Twitter/X**: Tweet scheduling and monitoring
- **LinkedIn**: Professional content management
- **Telegram**: Bot integration and channel management

### 📊 Advanced Analytics & Insights
- **Real-time Analytics**: Track engagement, reach, and performance metrics
- **Sentiment Analysis**: Monitor comment sentiment and audience feedback
- **Performance Insights**: Data-driven recommendations for content strategy
- **Custom Reports**: Generate detailed analytics reports

### ⏰ Smart Scheduling
- **Content Calendar**: Visual planning and scheduling interface
- **Optimal Timing**: AI-suggested best posting times
- **Bulk Scheduling**: Schedule multiple posts across platforms
- **Recurring Posts**: Set up automated recurring content

### 💬 Intelligent Comment Management
- **Auto-Reply**: AI-powered automatic responses to comments
- **Sentiment Monitoring**: Track and analyze comment sentiment
- **Moderation Tools**: Filter and manage inappropriate content
- **Engagement Analytics**: Measure comment engagement effectiveness

### 🎯 Strategy Management
- **Content Strategies**: Create and manage content strategies
- **A/B Testing**: Test different content approaches
- **Performance Tracking**: Monitor strategy effectiveness
- **Goal Setting**: Set and track social media objectives

## 🏗️ Architecture

Rominext follows a modular, service-oriented architecture:

```
├── app/                    # Main application package
│   ├── blueprints/        # Flask blueprints for routing
│   ├── models/            # Database models (MongoDB)
│   ├── services/          # Business logic layer
│   ├── integrations/      # External service integrations
│   │   ├── ai/           # AI provider integrations
│   │   └── social/       # Social media connectors
│   ├── templates/         # Jinja2 templates
│   ├── static/           # Static assets (CSS, JS, images)
│   └── utils/            # Utility functions
├── api/                   # API endpoints
├── bot/                   # Telegram bot implementation
├── config/               # Configuration files
├── docs/                 # Documentation
├── tests/                # Test suite
└── scripts/              # Utility scripts
```

## 🛠️ Technology Stack

### Backend
- **Framework**: Flask 2.2.5
- **Database**: MongoDB with MongoEngine ODM
- **Authentication**: Flask-Login
- **API Documentation**: Swagger UI
- **Task Queue**: Background workers for scheduling

### AI & Machine Learning
- **AI Providers**: OpenAI, DeepSeek, Grok, HuggingFace, TogetherAI, StabilityAI, DeepInfra
- **Language Processing**: LangChain for AI workflow management
- **Content Analysis**: Sentiment analysis and content optimization

### Social Media Integration
- **APIs**: Facebook Graph API, Instagram Basic Display API, Twitter API v2, LinkedIn API
- **Bot Framework**: python-telegram-bot for Telegram integration
- **Real-time Updates**: Webhook support for instant notifications

### Frontend
- **Templates**: Jinja2 with responsive design
- **Styling**: Modern CSS with responsive layouts
- **JavaScript**: Interactive dashboards and real-time updates
- **Charts**: Plotly for analytics visualization

### DevOps & Deployment
- **Containerization**: Docker and Docker Compose
- **Process Management**: Gunicorn for production
- **Logging**: Structured logging with rotation
- **Monitoring**: Health checks and system monitoring

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- MongoDB 5.0+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/rominext.git
   cd rominext
   ```

2. **Set up virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Initialize database**
   ```bash
   python run.py init-db
   ```

6. **Create admin user**
   ```bash
   python run.py create-admin
   ```

7. **Start the application**
   ```bash
   python run.py run --debug
   ```

8. **Access the application**
   - Web Interface: http://localhost:5000
   - API Documentation: http://localhost:5000/api/docs/

## 📖 Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[Installation Guide](docs/installation.md)** - Detailed setup instructions
- **[Configuration Guide](docs/configuration.md)** - Environment variables and settings
- **[API Documentation](docs/api.md)** - REST API reference
- **[User Guide](docs/user-guide.md)** - Platform usage instructions
- **[Developer Guide](docs/developer-guide.md)** - Contributing and development
- **[Architecture Guide](docs/architecture.md)** - System design and components
- **[Deployment Guide](docs/deployment.md)** - Production deployment
- **[Testing Guide](docs/testing.md)** - Running and writing tests
- **[Troubleshooting](docs/troubleshooting.md)** - Common issues and solutions

## 🔧 Configuration

Key environment variables:

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/rominext

# Security
SECRET_KEY=your-secret-key-here

# AI Providers
OPENAI_API_KEY=your-openai-key
DEEPSEEK_API_KEY=your-deepseek-key
GROK_API_KEY=your-grok-key

# Social Media APIs
FACEBOOK_PAGE_ACCESS_TOKEN=your-facebook-token
INSTAGRAM_ACCESS_TOKEN=your-instagram-token
TWITTER_BEARER_TOKEN=your-twitter-token
LINKEDIN_ACCESS_TOKEN=your-linkedin-token

# Telegram Bot
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/models/test_user.py
```

## 🚀 Deployment

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f
```

### Production Deployment

```bash
# Install production dependencies
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

## 🤝 Contributing

We welcome contributions! Please see our [Developer Guide](docs/developer-guide.md) for details on:

- Setting up the development environment
- Code style and standards
- Submitting pull requests
- Reporting issues

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Report bugs on [GitHub Issues](https://github.com/your-org/rominext/issues)
- **Discussions**: Join our [GitHub Discussions](https://github.com/your-org/rominext/discussions)

## 🙏 Acknowledgments

- Flask community for the excellent web framework
- MongoDB for the flexible database solution
- All AI providers for their powerful APIs
- Social media platforms for their developer APIs
- Open source community for the amazing tools and libraries

---

**Made with ❤️ by the Rominext Team**
