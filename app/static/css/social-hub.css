/* AI Social Hub Animation */
.ai-social-hub {
  height: 400px;
  width: 400px;
  position: relative;
}

/* Central AI core - change from blue to green */
.ai-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #28a745 0%, #5cb85c 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 0 30px rgba(40, 167, 69, 0.5);
}

.ai-icon {
  color: white;
}

/* Pulsing effect - change from blue to green */
.ai-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(40, 167, 69, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.5); opacity: 0; }
  100% { transform: scale(1); opacity: 0; }
}

/* Connection lines - change from blue to green */
.connection-lines .line {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 2px;
  background: linear-gradient(90deg, rgba(40, 167, 69, 0.8), rgba(92, 184, 92, 0.2));
  transform-origin: left center;
  animation: dataFlow 3s infinite linear;
  z-index: 1;
}

.line-1 { width: 160px; transform: rotate(0deg); }
.line-2 { width: 160px; transform: rotate(45deg); }
.line-3 { width: 160px; transform: rotate(90deg); }
.line-4 { width: 160px; transform: rotate(135deg); }
.line-5 { width: 160px; transform: rotate(180deg); }
.line-6 { width: 160px; transform: rotate(225deg); }
.line-7 { width: 160px; transform: rotate(270deg); }
.line-8 { width: 160px; transform: rotate(315deg); }

@keyframes dataFlow {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 0%; }
}

/* Social platforms */
.social-platforms .platform {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 5;
  transition: all 0.3s ease;
  animation: float 4s infinite ease-in-out;
}

.platform:hover {
  transform: scale(1.2);
}

.platform i {
  font-size: 20px;
}

.platform-data {
  position: absolute;
  bottom: -20px;
  font-size: 12px;
  font-weight: bold;
  color: #28a745;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.platform:hover .platform-data {
  opacity: 1;
}

/* Position social platforms precisely at line ends */
.p-facebook { 
  top: calc(50% - 25px); 
  left: calc(50% + 160px - 25px); 
}
.p-twitter { 
  top: calc(50% - 25px - 113px); 
  left: calc(50% + 113px - 25px); 
}
.p-instagram { 
  top: calc(50% - 160px - 25px); 
  left: calc(50% - 25px); 
}
.p-linkedin { 
  top: calc(50% - 25px - 113px); 
  left: calc(50% - 113px - 25px); 
}
.p-youtube { 
  top: calc(50% - 25px); 
  left: calc(50% - 160px - 25px); 
}
.p-tiktok { 
  top: calc(50% + 113px - 25px); 
  left: calc(50% - 113px - 25px); 
}
.p-pinterest { 
  top: calc(50% + 160px - 25px); 
  left: calc(50% - 25px); 
}
.p-telegram { 
  top: calc(50% + 113px - 25px); 
  left: calc(50% + 113px - 25px); 
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Data particles - change from blue to green */
.data-particles .particle {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(40, 167, 69, 0.6);
  animation: particleMove 8s infinite linear;
}

.p1 { top: 30%; left: 40%; animation-delay: 0s; }
.p2 { top: 60%; left: 30%; animation-delay: 1s; }
.p3 { top: 40%; left: 70%; animation-delay: 2s; }
.p4 { top: 70%; left: 60%; animation-delay: 3s; }
.p5 { top: 50%; left: 50%; animation-delay: 4s; }
.p6 { top: 35%; left: 65%; animation-delay: 5s; }

@keyframes particleMove {
  0% { transform: translate(0, 0) scale(1); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translate(100px, -100px) scale(0); opacity: 0; }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .ai-social-hub {
    height: 300px;
    width: 300px;
  }
}


