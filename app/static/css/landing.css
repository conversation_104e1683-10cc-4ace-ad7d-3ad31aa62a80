/* Add this at the top of the file to apply to all landing page sections */
.landing-page {
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

/* Remove any section-specific backgrounds */
#features, .how-it-works, .modern-comparison-section, .premium-cta-section {
    background: transparent;
}

/* Persian font styling */
html[dir="rtl"] body,
html[dir="rtl"] h1,
html[dir="rtl"] h2,
html[dir="rtl"] h3,
html[dir="rtl"] h4,
html[dir="rtl"] h5,
html[dir="rtl"] h6,
html[dir="rtl"] p,
html[dir="rtl"] a,
html[dir="rtl"] button,
html[dir="rtl"] input,
html[dir="rtl"] textarea,
html[dir="rtl"] select,
html[dir="rtl"] .btn {
    font-family: 'Vazir', Tahoma, Arial, sans-serif !important;
}

/* General section styling */
.section-title {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
  color: #2c3e50;
}

.section-title:after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: #28a745;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.section-subtitle {
  color: #7f8c8d;
  max-width: 700px;
  margin: 0 auto;
}

/* Features section styling */
#features {
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  padding-top: 80px;
  padding-bottom: 80px;
}

.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  border-radius: 10px;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-icon i {
  font-size: 28px;
}

.feature-card .card-title {
  font-weight: 600;
  margin-top: 15px;
  color: #2c3e50;
}

.feature-card .card-text {
  color: #7f8c8d;
}

/* How It Works section styling */
.how-it-works {
  background-color: #ffffff;
  padding-top: 80px;
  padding-bottom: 80px;
}

.step-card {
  padding: 30px 20px;
  transition: transform 0.3s ease;
}

.step-card:hover {
  transform: translateY(-5px);
}

.step-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  position: relative;
}

.step-circle:after {
  content: '';
  position: absolute;
  width: 70px;
  height: 70px;
  border: 2px dashed #28a745;
  border-radius: 50%;
  top: -5px;
  left: -5px;
}

.step-title {
  font-weight: 600;
  margin-top: 20px;
  color: #2c3e50;
}

.step-description {
  color: #7f8c8d;
  margin-top: 10px;
}

/* Add connecting lines between steps */
@media (min-width: 768px) {
  .how-it-works .row:before {
    content: '';
    position: absolute;
    top: 90px;
    left: 25%;
    width: 50%;
    height: 2px;
    background: linear-gradient(to right, transparent, #28a745, transparent);
  }
}

/* RTL adjustments for Persian */
html[dir="rtl"] .section-title:after {
  right: 50%;
  left: auto;
  transform: translateX(50%);
}

html[dir="rtl"] .feature-card .card-text,
html[dir="rtl"] .step-description {
  text-align: right;
}

/* Comparison section styling */
.comparison-container {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.comparison-container h3 {
  font-weight: 600;
}

.comparison-container .text-danger {
  color: #dc3545 !important;
}

.comparison-container .text-success {
  color: #28a745 !important;
}

.comparison-container ul li {
  position: relative;
  padding: 8px 0;
}

/* Add a subtle divider between columns on desktop */
@media (min-width: 768px) {
  .comparison-container .col-md-6:first-child {
    border-right: 1px dashed #dee2e6;
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .comparison-container .col-md-6:first-child {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px dashed #dee2e6;
  }
}

/* Divider styling */
.comparison-divider {
  position: absolute;
  top: 0;
  left: 50%;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #f8f9fa, #28a745);
  z-index: 10;
  transform: translateX(-50%);
}

.divider-flow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to bottom, transparent, #28a745, transparent);
  animation: flowDown 3s infinite;
}

/* Traditional side styling */
.comparison-side {
  flex: 1;
  padding: 40px;
  transition: all 0.4s ease;
  position: relative;
}

.traditional-side {
  background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
  animation: subtleShake 15s infinite;
}

.traditional-side:hover {
  animation: shake 0.5s infinite;
}

.rominext-side {
  background: linear-gradient(145deg, #f0f8f0, #e8f5e9);
}

.rominext-side:hover {
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(40, 167, 69, 0.2);
}

/* Content styling */
.comparison-content {
  max-width: 90%;
  margin: 0 auto;
}

.comparison-list {
  list-style: none;
  padding: 0;
}

.comparison-list li {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.traditional-side .comparison-list li:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateX(5px);
}

.rominext-side .comparison-list li:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateX(-5px);
}

/* Animations */
.traditional-animation, .rominext-animation {
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 20px 0;
}

.stressed-human {
  position: relative;
}

.typing-hands {
  width: 30px;
  height: 20px;
  background-color: #888;
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  animation: typing 0.5s infinite alternate;
}

.sweat-drop {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #3498db;
  border-radius: 50%;
  opacity: 0;
  animation: sweatDrop 3s infinite;
}

.paper-stack {
  position: absolute;
  bottom: -40px;
  right: -30px;
  width: 40px;
  height: 50px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  transform: rotate(10deg);
}

.paper-stack:before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  width: 40px;
  height: 50px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  z-index: -1;
}

.ticking-clock {
  position: absolute;
  top: -30px;
  left: -30px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #333;
  animation: ticking 1s infinite linear;
}

.ai-assistant {
  position: relative;
}

.magic-wand {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 30px;
  height: 5px;
  background-color: #f8f9fa;
  transform: rotate(45deg);
}

.magic-wand:after {
  content: '✨';
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 20px;
  animation: sparkle 2s infinite;
}

.flying-content {
  position: absolute;
  width: 20px;
  height: 25px;
  background-color: white;
  border: 1px solid #ddd;
  animation: flyContent 3s infinite;
}

.sparkle-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.sparkle-effect:after {
  content: '✨';
  position: absolute;
  font-size: 15px;
  opacity: 0;
  animation: randomSparkle 4s infinite;
}

/* Keyframe animations */
@keyframes flowDown {
  0% { top: -30%; }
  100% { top: 100%; }
}

@keyframes subtleShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(2px); }
  75% { transform: translateX(-2px); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(3px); }
  75% { transform: translateX(-3px); }
}

@keyframes typing {
  0% { transform: translateX(-50%) translateY(0); }
  100% { transform: translateX(-50%) translateY(5px); }
}

@keyframes sweatDrop {
  0%, 100% { opacity: 0; top: 0; }
  50% { opacity: 1; top: 20px; }
}

@keyframes ticking {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes flyContent {
  0% { transform: translate(0, 0) rotate(0deg); opacity: 0; }
  30% { opacity: 1; }
  100% { transform: translate(50px, -30px) rotate(10deg); opacity: 0; }
}

@keyframes randomSparkle {
  0%, 100% { opacity: 0; top: 50%; left: 50%; }
  25% { opacity: 1; top: 25%; left: 75%; }
  50% { opacity: 1; top: 75%; left: 25%; }
  75% { opacity: 1; top: 40%; left: 60%; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comparison-container {
    flex-direction: column;
  }
  
  .comparison-divider {
    left: 0;
    top: 50%;
    width: 100%;
    height: 4px;
    transform: translateY(-50%);
  }
  
  .divider-flow {
    height: 100%;
    width: 30%;
    animation: flowRight 3s infinite;
  }
  
  @keyframes flowRight {
    0% { left: -30%; }
    100% { left: 100%; }
  }
}

/* Light blue box styling */
.bg-light-blue {
  background-color: #f0f7ff;
  border: 1px solid #e0e8f5;
  border-radius: 8px;
}

.comparison-container {
  overflow: hidden;
}

.comparison-container h2 {
  color: #333;
  margin-bottom: 20px;
}

/* Side-by-side comparison styling */
.comparison-box {
  border: 1px solid rgba(0, 123, 255, 0.2);
  transition: all 0.3s ease;
}

.comparison-box:hover {
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1) !important;
}

/* Adjust for mobile */
@media (max-width: 767px) {
  .comparison-box .row {
    flex-direction: column;
  }
  
  .comparison-box .col-md-6:first-child {
    margin-bottom: 2rem;
  }
}

/* Fix for oversized SVG icons on pricing page */
.pricing-card svg, 
.container svg {
  max-width: 24px !important;
  max-height: 24px !important;
  width: 24px !important;
  height: 24px !important;
  overflow: visible;
}

.pricing-card .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  margin: 0 auto;
}
