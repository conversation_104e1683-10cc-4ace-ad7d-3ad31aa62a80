/* Force RTL on all elements */
html[dir="rtl"] * {
    direction: rtl !important;
}

/* Layout RTL fixes */
html[dir="rtl"] body,
html[dir="rtl"] .container,
html[dir="rtl"] .container-fluid,
html[dir="rtl"] .row,
html[dir="rtl"] .col,
html[dir="rtl"] [class*="col-"] {
    direction: rtl !important;
    text-align: right !important;
}

/* Navigation RTL fixes */
html[dir="rtl"] .navbar,
html[dir="rtl"] .navbar-nav,
html[dir="rtl"] .nav {
    direction: rtl !important;
    padding-right: 0 !important;
}

html[dir="rtl"] .navbar .nav-link {
    text-align: right !important;
}

/* Dashboard components RTL */
html[dir="rtl"] .card,
html[dir="rtl"] .card-header,
html[dir="rtl"] .card-body,
html[dir="rtl"] .card-footer {
    direction: rtl !important;
    text-align: right !important;
}

html[dir="rtl"] .list-group,
html[dir="rtl"] .list-group-item {
    direction: rtl !important;
    text-align: right !important;
}

/* Form controls RTL */
html[dir="rtl"] .form-control,
html[dir="rtl"] .form-select,
html[dir="rtl"] .input-group {
    direction: rtl !important;
    text-align: right !important;
}

html[dir="rtl"] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px !important;
    margin-left: 0 !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 0.375rem !important;
    border-bottom-left-radius: 0.375rem !important;
}

html[dir="rtl"] .input-group > :first-child:not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

/* Tables RTL */
html[dir="rtl"] .table th,
html[dir="rtl"] .table td {
    text-align: right !important;
}

/* Flexbox RTL fixes */
html[dir="rtl"] .d-flex {
    direction: rtl !important;
}

html[dir="rtl"] .justify-content-end {
    justify-content: flex-start !important;
}

html[dir="rtl"] .justify-content-start {
    justify-content: flex-end !important;
}

/* Margin and padding RTL fixes */
html[dir="rtl"] [class*="ms-"] {
    margin-right: var(--bs-gutter-x) !important;
    margin-left: 0 !important;
}

html[dir="rtl"] [class*="me-"] {
    margin-left: var(--bs-gutter-x) !important;
    margin-right: 0 !important;
}

html[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

html[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Float RTL fixes */
html[dir="rtl"] .float-start {
    float: right !important;
}

html[dir="rtl"] .float-end {
    float: left !important;
}

/* Text alignment RTL fixes */
html[dir="rtl"] .text-start {
    text-align: right !important;
}

html[dir="rtl"] .text-end {
    text-align: left !important;
}

/* Dropdown menus RTL */
html[dir="rtl"] .dropdown-menu {
    text-align: right !important;
    left: auto !important;
    right: 0 !important;
}

/* Button groups RTL */
html[dir="rtl"] .btn-group {
    direction: rtl !important;
}

html[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-radius: 0 0.375rem 0.375rem 0 !important;
}

html[dir="rtl"] .btn-group > .btn:not(:first-child) {
    border-radius: 0.375rem 0 0 0.375rem !important;
    margin-right: -1px !important;
    margin-left: 0 !important;
}

/* Custom dashboard elements RTL */
html[dir="rtl"] .activity-insights,
html[dir="rtl"] .recent-posts,
html[dir="rtl"] .recent-comments,
html[dir="rtl"] .statistics,
html[dir="rtl"] .comment-card,
html[dir="rtl"] .suggestion-item {
    direction: rtl !important;
    text-align: right !important;
}

/* Fix Bootstrap grid system for RTL */
html[dir="rtl"] .offset-1 { margin-right: 8.333333% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-2 { margin-right: 16.666667% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-3 { margin-right: 25% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-4 { margin-right: 33.333333% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-5 { margin-right: 41.666667% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-6 { margin-right: 50% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-7 { margin-right: 58.333333% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-8 { margin-right: 66.666667% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-9 { margin-right: 75% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-10 { margin-right: 83.333333% !important; margin-left: 0 !important; }
html[dir="rtl"] .offset-11 { margin-right: 91.666667% !important; margin-left: 0 !important; }

/* Fix icon positioning */
html[dir="rtl"] .fas,
html[dir="rtl"] .far,
html[dir="rtl"] .fab {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

/* Fix Bootstrap utilities */
html[dir="rtl"] .pe-0 { padding-left: 0 !important; padding-right: initial !important; }
html[dir="rtl"] .ps-0 { padding-right: 0 !important; padding-left: initial !important; }
html[dir="rtl"] .pe-1 { padding-left: 0.25rem !important; padding-right: initial !important; }
html[dir="rtl"] .ps-1 { padding-right: 0.25rem !important; padding-left: initial !important; }
html[dir="rtl"] .pe-2 { padding-left: 0.5rem !important; padding-right: initial !important; }
html[dir="rtl"] .ps-2 { padding-right: 0.5rem !important; padding-left: initial !important; }
html[dir="rtl"] .pe-3 { padding-left: 1rem !important; padding-right: initial !important; }
html[dir="rtl"] .ps-3 { padding-right: 1rem !important; padding-left: initial !important; }
html[dir="rtl"] .pe-4 { padding-left: 1.5rem !important; padding-right: initial !important; }
html[dir="rtl"] .ps-4 { padding-right: 1.5rem !important; padding-left: initial !important; }
html[dir="rtl"] .pe-5 { padding-left: 3rem !important; padding-right: initial !important; }
html[dir="rtl"] .ps-5 { padding-right: 3rem !important; padding-left: initial !important; } 