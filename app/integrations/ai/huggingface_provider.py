import os
import requests
from .base_ai_provider import BaseAIProvider

class HuggingFaceProvider(BaseAIProvider):
    """
    Concrete implementation of BaseAIProvider for HuggingFace Inference API services.
    """

    def __init__(self, api_key=None, base_url="https://api-inference.huggingface.co/models", cache=None):
        """
        Initialize HuggingFace provider with API key and base URL.
        
        :param api_key: The HuggingFace API key (optional, will use env var if not provided)
        :param base_url: The base URL for HuggingFace Inference API
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key or os.getenv("HUGGINGFACE_API_KEY")
        super().__init__(base_url=base_url, cache=cache)

    def authenticate(self):
        """
        Authenticate with HuggingFace API.
        For HuggingFace, we just need to store the API key.
        """
        if not self.api_key:
            import logging
            logging.warning("HuggingFace API key is not set. Some functionality may be limited.")
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for API requests.
        """
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        return headers

    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a request to the HuggingFace Inference API.
        
        :param endpoint: The specific model endpoint to call
        :param payload: The payload to send in the request
        :return: The raw response from the API
        """
        self.log_request(endpoint, payload)

        headers = self.get_headers()

        response = requests.post(
            url=f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}",
            headers=headers,
            json=payload
        )

        if response.ok:
            response_data = response.json()
            self.cache_response(endpoint, response_data)
            return response_data
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response from HuggingFace API to extract generated content.
        """
        # HuggingFace response format varies by model type
        if isinstance(response, list) and len(response) > 0:
            if "generated_text" in response[0]:
                return {"text": response[0]["generated_text"]}
        elif isinstance(response, dict):
            if "generated_text" in response:
                return {"text": response["generated_text"]}
        
        # Return the raw response if we can't extract in a standard way
        return {"text": str(response), "raw_response": response}

    def generate_text(self, prompt: str, model: str = "gpt2", max_tokens: int = 100) -> dict:
        """
        Generate text using HuggingFace's text generation models.
        """
        endpoint = model  # HuggingFace uses model name as endpoint
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": 0.7,
                "return_full_text": False
            }
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def chat_completion(self, messages: list, model: str = "facebook/blenderbot-400M-distill") -> dict:
        """
        Chat with HuggingFace's conversational models.
        """
        endpoint = model
        
        # Format messages into a single prompt for non-chat models
        if len(messages) > 0:
            last_message = messages[-1]["content"] if isinstance(messages[-1], dict) else messages[-1]
        else:
            last_message = ""
            
        payload = {
            "inputs": last_message,
            "parameters": {
                "return_full_text": False
            }
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
            
    def image_to_text(self, image_url: str, model: str = "google/matcha-chartqa") -> dict:
        """
        Process images and generate text descriptions or answers using vision models.
        """
        endpoint = model
        
        payload = {
            "inputs": image_url
        }
        
        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
