import requests
from typing import Dict, Any, List
from .base_social_connector import BaseSocialIntegration as BaseConnector

class TelegramConnector(BaseConnector):
    """
    Integration class for Telegram Bot API
    """

    def __init__(self, auth_data: dict):
        self.bot_token = auth_data.get("bot_token")
        self.chat_id = auth_data.get("chat_id")  # Channel ID or Group ID
        self.api_base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.supported_features = ["post", "edit", "delete", "fetch_recent"]

    def authenticate(self) -> bool:
        url = f"{self.api_base_url}/getMe"
        resp = requests.get(url)
        return resp.status_code == 200

    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a message (text, image with caption, link)
        """

        message = content_data.get("message", "")
        image_url = content_data.get("image_url")

        if image_url:
            url = f"{self.api_base_url}/sendPhoto"
            payload = {
                "chat_id": self.chat_id,
                "photo": image_url,
                "caption": message
            }
        else:
            url = f"{self.api_base_url}/sendMessage"
            payload = {
                "chat_id": self.chat_id,
                "text": message
            }

        resp = requests.post(url, data=payload)
        resp.raise_for_status()
        return resp.json()

    def edit_content(self, post_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Edit a text message
        """
        new_text = updated_data.get("message")
        if not new_text:
            raise ValueError("Updated message text is required.")

        url = f"{self.api_base_url}/editMessageText"
        payload = {
            "chat_id": self.chat_id,
            "message_id": post_id,
            "text": new_text
        }

        resp = requests.post(url, data=payload)
        return resp.status_code == 200

    def delete_content(self, post_id: str) -> bool:
        """
        Delete a message by ID
        """
        url = f"{self.api_base_url}/deleteMessage"
        payload = {
            "chat_id": self.chat_id,
            "message_id": post_id
        }

        resp = requests.post(url, data=payload)
        return resp.status_code == 200

    def fetch_recent_posts(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Telegram Bot API does not allow fetching previous messages directly.
        You need to enable a webhook or polling.
        So this is just a stub.
        """
        raise NotImplementedError("Telegram Bots cannot fetch old messages via Bot API.")

    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch bot information
        """
        url = f"{self.api_base_url}/getMe"
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json().get("result", {})

    def fetch_metrics(self, post_id: str) -> Dict[str, Any]:
        """
        Telegram Bot API doesn't offer post metrics.
        """
        raise NotImplementedError("Telegram does not provide post metrics via Bot API.")

    def verify_connection(self) -> bool:
        return self.authenticate()
