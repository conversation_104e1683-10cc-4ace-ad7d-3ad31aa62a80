import requests
from typing import Dict, Any, List
from datetime import datetime
from .base_social_connector import BaseSocialIntegration as BaseConnector

class InstagramConnector(BaseConnector):
    """
    Integration class for Instagram Graph API
    """

    def __init__(self, auth_data: dict):
        self.access_token = auth_data.get("access_token")
        self.instagram_account_id = auth_data.get("instagram_account_id")  # Business account ID
        self.api_base_url = "https://graph.facebook.com/v18.0"  # Update if API version changes
        self.supported_features = ["post", "edit", "delete", "metrics", "comments", "reels"]

    def authenticate(self) -> bool:
        # Simple token validation
        url = f"{self.api_base_url}/me?access_token={self.access_token}"
        response = requests.get(url)
        return response.status_code == 200

    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Publish an image or video post.
        """

        media_type = content_data.get("media_type", "IMAGE")  # IMAGE or VIDEO
        media_url = content_data.get("media_url")
        caption = content_data.get("caption", "")

        # 1. Create media object
        create_media_url = f"{self.api_base_url}/{self.instagram_account_id}/media"
        payload = {
            "image_url" if media_type == "IMAGE" else "video_url": media_url,
            "caption": caption,
            "access_token": self.access_token
        }
        media_resp = requests.post(create_media_url, data=payload)
        media_resp.raise_for_status()

        creation_id = media_resp.json().get("id")

        # 2. Publish media object
        publish_url = f"{self.api_base_url}/{self.instagram_account_id}/media_publish"
        publish_resp = requests.post(publish_url, data={
            "creation_id": creation_id,
            "access_token": self.access_token
        })
        publish_resp.raise_for_status()

        return publish_resp.json()

    def edit_content(self, post_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Instagram API only allows limited editing (mainly caption edits).
        """
        caption = updated_data.get("caption")
        if not caption:
            raise ValueError("Instagram only supports editing caption.")

        url = f"{self.api_base_url}/{post_id}"
        resp = requests.post(url, data={
            "caption": caption,
            "access_token": self.access_token
        })
        return resp.status_code == 200

    def delete_content(self, post_id: str) -> bool:
        """
        Delete a post
        """
        url = f"{self.api_base_url}/{post_id}?access_token={self.access_token}"
        resp = requests.delete(url)
        return resp.status_code == 200

    def fetch_metrics(self, post_id: str) -> Dict[str, Any]:
        """
        Fetch likes, comments, reach, etc.
        """
        url = f"{self.api_base_url}/{post_id}/insights"
        params = {
            "metric": "impressions,reach,engagement,saved",
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()

    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch Instagram profile info
        """
        url = f"{self.api_base_url}/{self.instagram_account_id}"
        params = {
            "fields": "id,username,followers_count,follows_count,media_count,profile_picture_url",
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()

    def fetch_recent_posts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Fetch recent media
        """
        url = f"{self.api_base_url}/{self.instagram_account_id}/media"
        params = {
            "fields": "id,caption,media_type,media_url,timestamp,permalink",
            "limit": limit,
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

    def schedule_content(self, content_data: Dict[str, Any], scheduled_time: str) -> Dict[str, Any]:
        """
        Instagram now supports scheduled posts up to 75 days ahead.
        scheduled_time must be in ISO8601 format.
        """
        media_type = content_data.get("media_type", "IMAGE")
        media_url = content_data.get("media_url")
        caption = content_data.get("caption", "")
        scheduled_unix_timestamp = int(datetime.fromisoformat(scheduled_time).timestamp())

        # 1. Create container with schedule_time
        create_media_url = f"{self.api_base_url}/{self.instagram_account_id}/media"
        payload = {
            "image_url" if media_type == "IMAGE" else "video_url": media_url,
            "caption": caption,
            "access_token": self.access_token,
            "published": False,
            "scheduled_publish_time": scheduled_unix_timestamp
        }
        media_resp = requests.post(create_media_url, data=payload)
        media_resp.raise_for_status()

        creation_id = media_resp.json().get("id")

        # 2. Publish the container
        publish_url = f"{self.api_base_url}/{self.instagram_account_id}/media_publish"
        publish_resp = requests.post(publish_url, data={
            "creation_id": creation_id,
            "access_token": self.access_token
        })
        publish_resp.raise_for_status()

        return publish_resp.json()

    def upload_media(self, media_path: str, media_type: str) -> str:
        """
        Uploading local media is not directly supported.
        You must first upload media publicly accessible (URL).
        This function is a placeholder.
        """
        raise NotImplementedError("Instagram Graph API requires public URLs for media uploads.")

    def verify_connection(self) -> bool:
        return self.authenticate()

    def post_comment(self, post_id: str, comment_text: str) -> Dict[str, Any]:
        url = f"{self.api_base_url}/{post_id}/comments"
        payload = {
            "message": comment_text,
            "access_token": self.access_token
        }
        resp = requests.post(url, data=payload)
        resp.raise_for_status()
        return resp.json()

    def delete_comment(self, comment_id: str) -> bool:
        url = f"{self.api_base_url}/{comment_id}?access_token={self.access_token}"
        resp = requests.delete(url)
        return resp.status_code == 200

    def fetch_comments(self, post_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        url = f"{self.api_base_url}/{post_id}/comments"
        params = {
            "limit": limit,
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

