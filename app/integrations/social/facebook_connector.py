import requests
from typing import Dict, Any, List
from .base_social_connector import BaseSocialIntegration as BaseConnector

class FacebookConnector(BaseConnector):
    """
    Integration class for Facebook Graph API
    """

    def __init__(self, auth_data: dict):
        self.access_token = auth_data.get("access_token")
        self.page_id = auth_data.get("page_id")  # Facebook Page ID
        self.api_base_url = "https://graph.facebook.com/v18.0"  # Current Meta API version
        self.supported_features = ["post", "edit", "delete", "metrics", "comments"]

    def authenticate(self) -> bool:
        url = f"{self.api_base_url}/me?access_token={self.access_token}"
        response = requests.get(url)
        return response.status_code == 200

    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new post on Facebook page
        Supports text, link, image (basic publishing API)
        """

        message = content_data.get("message", "")
        link = content_data.get("link")
        image_url = content_data.get("image_url")

        payload = {
            "message": message,
            "access_token": self.access_token
        }

        if link:
            payload["link"] = link
        if image_url:
            payload["url"] = image_url

        url = f"{self.api_base_url}/{self.page_id}/feed"
        resp = requests.post(url, data=payload)
        resp.raise_for_status()
        return resp.json()

    def edit_content(self, post_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Edit a post (usually message only).
        """
        message = updated_data.get("message")
        if not message:
            raise ValueError("Facebook only supports editing the post message.")

        url = f"{self.api_base_url}/{post_id}"
        resp = requests.post(url, data={
            "message": message,
            "access_token": self.access_token
        })
        return resp.status_code == 200

    def delete_content(self, post_id: str) -> bool:
        url = f"{self.api_base_url}/{post_id}?access_token={self.access_token}"
        resp = requests.delete(url)
        return resp.status_code == 200

    def fetch_metrics(self, post_id: str) -> Dict[str, Any]:
        """
        Fetch insights for a post
        """
        url = f"{self.api_base_url}/{post_id}/insights"
        params = {
            "metric": "post_impressions,post_reactions_by_type_total,post_engaged_users",
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()

    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch Facebook Page profile info
        """
        url = f"{self.api_base_url}/{self.page_id}"
        params = {
            "fields": "id,name,fan_count,link",
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()

    def fetch_recent_posts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Fetch recent posts
        """
        url = f"{self.api_base_url}/{self.page_id}/posts"
        params = {
            "fields": "id,message,created_time,permalink_url",
            "limit": limit,
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

    def schedule_content(self, content_data: Dict[str, Any], scheduled_time: str) -> Dict[str, Any]:
        """
        Schedule posts for future
        scheduled_time: ISO 8601 format
        """
        from datetime import datetime
        scheduled_unix = int(datetime.fromisoformat(scheduled_time).timestamp())

        message = content_data.get("message", "")
        link = content_data.get("link")

        payload = {
            "message": message,
            "published": False,
            "scheduled_publish_time": scheduled_unix,
            "access_token": self.access_token
        }

        if link:
            payload["link"] = link

        url = f"{self.api_base_url}/{self.page_id}/feed"
        resp = requests.post(url, data=payload)
        resp.raise_for_status()
        return resp.json()

    def upload_media(self, media_path: str, media_type: str) -> str:
        """
        Upload media directly to Facebook (for example, for video posts)
        Note: Facebook expects multipart/form-data
        """

        # WARNING: for simplicity, this is a stub.
        raise NotImplementedError("Use Facebook Graph API video/photo upload endpoints.")

    def verify_connection(self) -> bool:
        return self.authenticate()

    def post_comment(self, post_id: str, comment_text: str) -> Dict[str, Any]:
        url = f"{self.api_base_url}/{post_id}/comments"
        payload = {
            "message": comment_text,
            "access_token": self.access_token
        }
        resp = requests.post(url, data=payload)
        resp.raise_for_status()
        return resp.json()

    def delete_comment(self, comment_id: str) -> bool:
        url = f"{self.api_base_url}/{comment_id}?access_token={self.access_token}"
        resp = requests.delete(url)
        return resp.status_code == 200

    def fetch_comments(self, post_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        url = f"{self.api_base_url}/{post_id}/comments"
        params = {
            "limit": limit,
            "access_token": self.access_token
        }
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

