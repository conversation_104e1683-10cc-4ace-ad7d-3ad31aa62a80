from flask import Flask, session, render_template, request, redirect, url_for
from flask_mongoengine import MongoEngine
from flask_login import LoginManager
from mongoengine import connect, disconnect
from datetime import datetime
from .config import get_settings
import logging
from app.services.translation_service import TranslationService
from .integrations.ai.openai_provider import OpenAIProvider
from .integrations.ai.deepseek_provider import DeepSeekProvider
from .integrations.ai.grok_provider import GrokAIProvider
from .integrations.ai.huggingface_provider import HuggingFaceProvider
from .integrations.ai.togetherai_provider import TogetherAIProvider
from .integrations.ai.stabilityai_provider import StabilityAIProvider
from .integrations.ai.deepinfra_provider import DeepInfraProvider
from .services.ai_provider_manager import AIProviderManager, UserAIProviderManager
import os
from flask_moment import Moment
import werkzeug.routing
from api.telegram_api import register_telegram_api

# Create moment instance
moment = Moment()

# Initialize social connectors
from .integrations.social.facebook_connector import FacebookConnector
from .integrations.social.twitter_connector import <PERSON><PERSON>onnector
from .integrations.social.instagram_connector import InstagramConnector
from .integrations.social.linkedin_connector import LinkedInConnector
from .services.social_connector_manager import SocialConnectorManager

# Import LogService
from .services.log_service import LogService

db = MongoEngine()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'  # Default login view

@login_manager.unauthorized_handler
def unauthorized():
    """Custom unauthorized handler to redirect to the appropriate login page"""
    if request.path.startswith('/admin'):
        return redirect(url_for('admin.login', next=request.url))
    return redirect(url_for('auth.login', next=request.url))

logger = logging.getLogger(__name__)

@login_manager.user_loader
def load_user(user_id):
    try:
        from .models import User
        return User.objects(id=user_id).first()
    except Exception as e:
        logger.error(f"Error loading user: {str(e)}")
        return None

def timeago_filter(value):
    """Convert a datetime string to a human-readable 'time ago' format."""
    try:
        dt = datetime.strptime(value, '%Y-%m-%dT%H:%M:%S+0000')
        now = datetime.utcnow()
        diff = now - dt

        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            return f"{diff.seconds // 3600} hours ago"
        elif diff.seconds > 60:
            return f"{diff.seconds // 60} minutes ago"
        else:
            return "just now"
    except Exception:
        return value

def create_app():
    app = Flask(__name__)
    settings = get_settings()
    app.config['SECRET_KEY'] = settings.SECRET_KEY
    app.config['MONGODB_SETTINGS'] = {
        'db': settings.MONGODB_DB,
        'host': settings.MONGODB_HOST,
        'port': settings.MONGODB_PORT,
        'username': settings.MONGODB_USERNAME,
        'password': settings.MONGODB_PASSWORD
    }
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    moment.init_app(app)  # Initialize Flask-Moment
    
    # Start log service worker
    LogService.start_worker()
    
    # Register teardown to stop log worker
    @app.teardown_appcontext
    def shutdown_log_service(exception=None):
        LogService.stop_worker()
    
    # Initialize translation service
    app.translation_service = TranslationService(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Set up global exception handler
    @app.before_request
    def before_request():
        # Store the start time for request duration calculation
        request._start_time = datetime.utcnow()
        
        # Set login view based on request path
        if request.path.startswith('/admin'):
            login_manager.login_view = 'admin.login'
        else:
            login_manager.login_view = 'auth.login'
    
    @app.after_request
    def after_request(response):
        # Log slow requests (more than 1 second)
        if hasattr(request, '_start_time'):
            duration = datetime.utcnow() - request._start_time
            if duration.total_seconds() > 1.0:
                from .utils.logging_helpers import log_event
                from .models.log import LogLevel, LogCategory
                
                log_event(
                    level=LogLevel.WARNING,
                    category=LogCategory.SYSTEM,
                    message=f"Slow request: {request.method} {request.path}",
                    details=f"Duration: {duration.total_seconds():.2f}s"
                )
        
        return response
    
    @app.errorhandler(Exception)
    def handle_exception(e):
        # Log unhandled exceptions
        from .utils.logging_helpers import log_event
        from .models.log import LogLevel, LogCategory
        import traceback
        
        # Get the full traceback
        error_details = traceback.format_exc()
        
        # Log to database
        log_event(
            level=LogLevel.ERROR,
            category=LogCategory.SYSTEM,
            message=f"Unhandled exception: {str(e)}",
            details=error_details
        )
        
        # Also log to file
        logger.error(f"Unhandled exception: {str(e)}")
        logger.error(error_details)
        
        # Return 500 error page
        lang = session.get('lang', 'fa')
        return render_template('errors/500.html', lang=lang), 500
    
    # Add translation function to template context
    @app.context_processor
    def utility_processor():
        def t(key):
            lang = session.get('lang', 'fa')
            return app.translation_service.translate(key, lang)
        return dict(t=t)
    
    # Register Jinja2 filters BEFORE registering blueprints
    app.jinja_env.filters['timeago'] = timeago_filter
    
    # Initialize AI providers with error handling
    providers = {}
    
    # Try to initialize OpenAI provider
    try:
        providers["openai"] = OpenAIProvider(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url="https://api.openai.com/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize OpenAI provider: {str(e)}")
    
    # Try to initialize DeepSeek provider
    try:
        providers["deepseek"] = DeepSeekProvider(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize DeepSeek provider: {str(e)}")
    
    # Try to initialize Grok provider
    try:
        providers["grok"] = GrokAIProvider(
            api_key=os.getenv("GROK_API_KEY"),
            base_url="https://api.grok.ai/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize Grok provider: {str(e)}")
    
    # Try to initialize HuggingFace provider
    try:
        providers["huggingface"] = HuggingFaceProvider(
            api_key=os.getenv("HUGGINGFACE_API_KEY"),
            base_url="https://api-inference.huggingface.co/models"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize HuggingFace provider: {str(e)}")
    
    # Try to initialize TogetherAI provider
    try:
        providers["togetherai"] = TogetherAIProvider(
            api_key=os.getenv("TOGETHER_API_KEY"),
            base_url="https://api.together.xyz/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize TogetherAI provider: {str(e)}")
    
    # Try to initialize StabilityAI provider
    try:
        providers["stabilityai"] = StabilityAIProvider(
            api_key=os.getenv("STABILITY_API_KEY"),
            base_url="https://api.stability.ai/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize StabilityAI provider: {str(e)}")
    
    # Try to initialize DeepInfra provider
    try:
        providers["deepinfra"] = DeepInfraProvider(
            api_key=os.getenv("DEEPINFRA_API_KEY"),
            base_url="https://api.deepinfra.com/v1"
        )
    except Exception as e:
        app.logger.warning(f"Failed to initialize DeepInfra provider: {str(e)}")
    
    # Create AI provider manager with configuration
    ai_provider_manager = AIProviderManager(
        providers,
        config_path=os.path.join(app.root_path, '..', 'config', 'ai_providers_config.yaml')
    )

    # Default provider is still set as fallback
    default_provider = app.config.get("DEFAULT_AI_PROVIDER", "openai")
    ai_provider_manager.set_default_provider(default_provider)
    
    # Make the manager available to the app
    app.ai_provider_manager = ai_provider_manager
    
    # Initialize social connectors
    facebook_connector = FacebookConnector({
        "access_token": os.getenv("FACEBOOK_PAGE_ACCESS_TOKEN", ""),
        "page_id": os.getenv("FACEBOOK_PAGE_ID", "")
    })
    
    twitter_connector = TwitterConnector({
        "bearer_token": os.getenv("TWITTER_BEARER_TOKEN", "")
    })
    
    instagram_connector = InstagramConnector({
        "access_token": os.getenv("INSTAGRAM_ACCESS_TOKEN", ""),
        "instagram_account_id": os.getenv("INSTAGRAM_ACCOUNT_ID", "")
    })
    
    linkedin_connector = LinkedInConnector({
        "access_token": os.getenv("LINKEDIN_ACCESS_TOKEN", "")
    })
    
    # Create social connector manager
    social_connector_manager = SocialConnectorManager({
        "facebook": facebook_connector,
        "twitter": twitter_connector,
        "instagram": instagram_connector,
        "linkedin": linkedin_connector
    })
    
    # Set default connector based on config
    default_platform = app.config.get("DEFAULT_SOCIAL_PLATFORM", "facebook")
    social_connector_manager.set_default_connector(default_platform)
    
    # Make the manager available to the app
    app.social_connector_manager = social_connector_manager
    
    # Register blueprints
    from .blueprints.vault_blueprint import vault_bp
    from .blueprints.dashboard_blueprint import dashboard_bp
    from .blueprints.post_blueprint import posts_bp, post_bp, content_bp
    from .blueprints.strategy_blueprint import strategy_bp
    from .blueprints.comment_blueprint import comments_bp
    from .blueprints.agent_blueprint import agent_bp
    from .blueprints.insight_blueprint import insight_bp
    from .blueprints.payment_blueprint import payment_bp
    from .blueprints.systemSetting_blueprint import system_setting_bp
    from .blueprints.landing_blueprint import landing_bp
    from .blueprints.scheduling_blueprint import scheduling_bp
    from .blueprints.account_blueprint import account_bp
    from .blueprints.log_blueprint import log_bp
    from .blueprints.admin_blueprint import admin_bp  # Make sure this is imported
    from .blueprints.blog_blueprint import blog_bp

    # Register the blueprints
    app.register_blueprint(vault_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(posts_bp)
    app.register_blueprint(post_bp)
    app.register_blueprint(content_bp)
    app.register_blueprint(strategy_bp)
    app.register_blueprint(comments_bp)
    app.register_blueprint(agent_bp)
    app.register_blueprint(insight_bp)
    app.register_blueprint(payment_bp)
    app.register_blueprint(system_setting_bp)
    app.register_blueprint(landing_bp)
    app.register_blueprint(scheduling_bp)
    app.register_blueprint(account_bp)
    app.register_blueprint(log_bp)
    app.register_blueprint(admin_bp)  # Make sure this is registered
    app.register_blueprint(blog_bp)

    # Set up AI provider and social connectors for dependency injection
    app.ai_provider = ai_provider_manager.default_provider
    app.social_connectors = [
        facebook_connector,
        twitter_connector,
        instagram_connector,
        linkedin_connector
    ]
    
    register_telegram_api(app)
    
    return app

def register_error_handlers(app):
    @app.errorhandler(401)
    def unauthorized_error(error):
        # Get language from session or default to Persian
        lang = session.get('lang', 'fa')
        
        # If the request is for the admin section, redirect to admin login
        if request.path.startswith('/admin'):
            return redirect(url_for('admin.login', next=request.url))
            
        return render_template('errors/401.html', lang=lang), 401
        
    @app.errorhandler(404)
    def not_found_error(error):
        # Get language from session or default to Persian
        lang = session.get('lang', 'fa')
        
        # If the request is for the admin section, check if it's a login attempt
        if request.path.startswith('/admin') and not request.path.startswith('/admin/login'):
            return redirect(url_for('admin.login'))
            
        return render_template('errors/404.html', lang=lang), 404
        
    @app.errorhandler(500)
    def server_error(error):
        # Get language from session or default to Persian
        lang = session.get('lang', 'fa')
        
        # Log the error to the database
        from .utils.logging_helpers import log_event
        from .models.log import LogLevel, LogCategory
        import traceback
        
        # Get the full traceback
        error_details = traceback.format_exc()
        
        # Log to database
        log_event(
            level=LogLevel.ERROR,
            category=LogCategory.SYSTEM,
            message=f"500 error: {str(error)}",
            details=error_details
        )
        
        # Also log to file for immediate debugging
        logger.error(f"500 error: {str(error)}")
        
        return render_template('errors/500.html', lang=lang), 500

    @app.errorhandler(werkzeug.routing.exceptions.BuildError)
    def handle_build_error(error):
        # Log the error to the database
        from .utils.logging_helpers import log_event
        from .models.log import LogLevel, LogCategory
        
        # Log to database
        log_event(
            level=LogLevel.ERROR,
            category=LogCategory.SYSTEM,
            message=f"URL build error: {str(error)}"
        )
        
        # Also log to file
        logger.error(f"URL build error: {str(error)}")
        
        lang = session.get('lang', 'fa')
        return render_template('errors/404.html', lang=lang), 404

# get_ai_provider and get_social_connectors functions removed

# Add this at the end of the file to explicitly export create_app
__all__ = ['create_app']
