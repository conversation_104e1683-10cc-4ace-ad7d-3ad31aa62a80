from flask import Blueprint, render_template, redirect, url_for, request, flash, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from ..models import User
from ..services.vault_service import VaultService
import logging

vault_bp = Blueprint('vault', __name__, url_prefix='/vault')
logger = logging.getLogger(__name__)

def translate(key: str) -> str:
    """Helper function to translate messages"""
    lang = session.get('lang', 'fa')
    return current_app.translation_service.translate(key, lang)

@vault_bp.route('/login', methods=['GET', 'POST'])
def login():
    # Redirect if already logged in
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    # Get language from session
    lang = session.get('lang', 'fa')
    
    # Handle login form submission
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = request.form.get('remember', False) == 'on'
        
        if not email or not password:
            flash(translate('auth.provide_credentials'), 'error')
            return render_template('dashboard/vaults/login.html', lang=lang)
        
        user = VaultService.verify_user(email, password)
        if user:
            login_user(user, remember=remember)
            flash(translate('auth.login_success'), 'success')
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard.index'))
        
        flash(translate('auth.invalid_credentials'), 'error')
        
    return render_template('dashboard/vaults/login.html', lang=lang)

@vault_bp.route('/register', methods=['GET', 'POST'])
def register():
    # Redirect if already logged in
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    # Get language from session
    lang = session.get('lang', 'fa')
    
    # Handle registration form submission
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        password = request.form.get('password')
        
        if not name or not email or not password:
            flash(translate('auth.provide_all_fields'), 'error')
            return render_template('dashboard/vaults/register.html', lang=lang)
        
        # Check if user already exists
        if User.objects(email=email).first():
            flash(translate('auth.email_exists'), 'error')
            return render_template('dashboard/vaults/register.html', lang=lang)
            
        try:
            user = VaultService.create_user(
                email=email,
                password=password,
                name=name  # Pass name directly to create_user
            )
            login_user(user)
            flash(translate('auth.register_success'), 'success')
            return redirect(url_for('dashboard.index'))
        except Exception as e:
            logger.error(f"Registration error: {str(e)}")
            flash(translate('auth.registration_failed'), 'error')
        
    return render_template('dashboard/vaults/register.html', lang=lang)

@vault_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('vault.login'))

@vault_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    # Get language from session
    lang = session.get('lang', 'fa')
    
    if request.method == 'POST':
        email = request.form.get('email')
        
        if not email:
            flash(translate('auth.provide_email'), 'error')
            return render_template('dashboard/vaults/forgot_password.html', lang=lang)
        
        # Check if user exists
        user = User.objects(email=email).first()
        if not user:
            # Don't reveal that the user doesn't exist
            flash(translate('auth.reset_link_sent'), 'success')
            return render_template('dashboard/vaults/forgot_password.html', lang=lang)
        
        # TODO: Implement password reset logic
        # For now, just show a success message
        flash(translate('auth.reset_link_sent'), 'success')
        
    return render_template('dashboard/vaults/forgot_password.html', lang=lang)


