from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session, current_app
from flask_login import login_required, current_user
from datetime import datetime
from ..models import Post, Strategy, ScheduledPost
from ..utils.logging_helpers import log_event
from ..models.log import LogLevel, LogCategory
import traceback

# Define blueprints
post_bp = Blueprint('dashboard_posts', __name__, url_prefix='/dashboard/posts')
posts_bp = Blueprint('public_posts', __name__, url_prefix='/posts')
content_bp = Blueprint('content', __name__, url_prefix='/content')  # Add content_bp definition

@post_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Get scheduling service with proper dependencies
    # Import here to avoid circular import
    from ..services.scheduling_service import SchedulingService
    
    scheduling_service = SchedulingService(
        ai_provider_manager=current_app.ai_provider_manager,
        social_connector_manager=current_app.social_connector_manager
    )
    
    if request.method == 'POST':
        content = request.form.get('content')
        schedule_time = request.form.get('schedule_time')
        platform = request.form.get('platform', None)  # Get selected platform
        
        if schedule_time:
            scheduled_time = datetime.strptime(schedule_time, '%Y-%m-%dT%H:%M')
            scheduling_service.schedule_post(current_user, content, scheduled_time, platform)
            flash('Post scheduled successfully!')
        else:
            # Direct posting
            if platform:
                connector = current_app.social_connector_manager.get_connector(platform)
                if connector:
                    connector.post_content({"message": content})
                    flash(f'Post created successfully on {platform}!')
            else:
                # Use default connector
                connector = current_app.social_connector_manager.get_default_connector()
                if connector:
                    connector.post_content({"message": content})
                    flash('Post created successfully!')
        
        return redirect(url_for('dashboard.index'))
    
    # Get available platforms for the template
    available_platforms = current_app.social_connector_manager.get_available_platforms()
    
    # Get language from session
    lang = session.get('lang', 'en')
    return render_template('dashboard/posts/create.html', 
                          lang=lang, 
                          platforms=available_platforms)

@post_bp.route('/suggest', methods=['POST'])
@login_required
def suggest_post():
    # Check if request is AJAX or form submit
    if request.is_json:
        topic = request.json.get('topic')
        platform = request.json.get('platform')  # Get platform from request
    else:
        topic = request.form.get('topic')
        platform = request.form.get('platform')
        
    if not topic:
        if request.is_json:
            return jsonify({'error': 'Topic is required'}), 400
        flash('Please provide a topic', 'error')
        return redirect(url_for('post_bp.create'))
    
    # Generate content suggestion with platform context if provided
    prompt = f"Generate a content suggestion about {topic}"
    if platform:
        prompt += f" specifically for {platform}"
    
    suggestion = current_app.ai_provider_manager.execute_with_failover(
        "generate_text",
        args=(prompt,),
        kwargs={"max_tokens": 300}
    ).get('text', '')
    
    if request.is_json:
        return jsonify({'suggestion': suggestion})
    
    # If not AJAX, set the suggestion in session and redirect
    session['post_suggestion'] = suggestion
    return redirect(url_for('dashboard_posts.create'))



from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from ..integrations.social.facebook_connector import FacebookConnector
from ..integrations.ai.openai_provider import OpenAIProvider

posts_bp = Blueprint('public_posts', __name__, url_prefix='/posts')  # Added url_prefix
facebook_connector = FacebookConnector({"access_token": "", "page_id": ""})
ai_provider = OpenAIProvider()

@posts_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_post():
    """Create a new post."""
    if request.method == 'POST':
        message = request.form.get('message')
        if not message:
            flash('Post message is required', 'error')
            return redirect(url_for('posts.create_post'))
        
        result = facebook_connector.create_post(message)
        if 'error' in result:
            flash(f'Error creating post: {result["error"]["message"]}', 'error')
        else:
            flash('Post created successfully!', 'success')
            return redirect(url_for('dashboard.index'))
    
    return render_template('posts/create.html')

# API endpoint for creating posts (used by the suggestion feature)
@posts_bp.route('/api/posts', methods=['POST'])
@login_required
def api_create_post():
    """API endpoint for creating posts."""
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'success': False, 'error': 'Message is required'}), 400
        
        message = data['message']
        result = facebook_connector.create_post(message)
        
        if isinstance(result, dict) and 'error' in result:
            return jsonify({'success': False, 'error': str(result['error'])}), 400
        
        return jsonify({'success': True, 'data': result}), 200
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from ..services.post_service import PostService
from marshmallow import Schema, fields, validate, ValidationError
import asyncio
from functools import wraps
import logging

logger = logging.getLogger(__name__)
post_bp = Blueprint('api_posts', __name__, url_prefix='/posts')

# Request validation schemas
class GeneratePostSchema(Schema):
    prompt = fields.String(required=True)
    content_type = fields.String(validate=validate.OneOf(["text", "image", "video"]), default="text")
    metadata = fields.Dict(missing=dict)

class SuggestPostSchema(Schema):
    topics = fields.List(fields.String(), missing=list)
    tone = fields.String(missing="professional")
    industry = fields.String(missing="general")
    count = fields.Integer(validate=validate.Range(min=1, max=10), missing=3)

class SerialContentSchema(Schema):
    topic = fields.String(required=True)
    parts = fields.Integer(validate=validate.Range(min=2, max=10), required=True)
    content_type = fields.String(validate=validate.OneOf(["text", "image", "video"]), default="text")
    metadata = fields.Dict(missing=dict)

class CampaignPostSchema(Schema):
    content = fields.String(required=True)
    content_type = fields.String(validate=validate.OneOf(["text", "image", "video"]), default="text")
    media_url = fields.String(missing="")

class ScheduleSchema(Schema):
    datetime = fields.DateTime(required=True)
    timezone = fields.String(missing="UTC")

class CampaignSchema(Schema):
    name = fields.String(required=True)
    posts = fields.List(fields.Nested(CampaignPostSchema), required=True)
    schedule = fields.List(fields.Nested(ScheduleSchema), required=True)

# Helper to run async functions in Flask
def async_route(f):
    @wraps(f)
    def wrapped(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapped

# Add this decorator to your API routes
def log_api_errors(f):
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        try:
            return await f(*args, **kwargs)
        except Exception as e:
            # Log the error
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.POST,
                message=f"API error in {f.__name__}: {str(e)}",
                details=traceback.format_exc(),
                user_id=str(current_user.id) if current_user.is_authenticated else None
            )
            # Re-raise the exception to be handled by the global handler
            raise
    return decorated_function

# Get PostService instance
def get_post_service():
    # Get AI provider and social connector from app context
    ai_provider = current_app.ai_provider_manager.default_provider
    social_connector = current_app.social_connector_manager.get_default_connector()
    
    # Get database session
    db_session = current_app.db.session
    
    return PostService(ai_provider, social_connector, db_session)

@post_bp.route('/generate', methods=['POST'])
@login_required
@async_route
@log_api_errors
async def generate_post():
    """Generate a new post using AI"""
    try:
        # Validate request data
        schema = GeneratePostSchema()
        data = schema.load(request.json)
        
        # Check user permissions
        if not current_user.is_active:
            return jsonify({"error": "User account is not active"}), 403
        
        # Get post service
        post_service = get_post_service()
        
        # Generate post
        result = await post_service.generate_post(
            prompt=data['prompt'],
            content_type=data['content_type'],
            metadata=data['metadata']
        )
        
        return jsonify({
            "success": True,
            "data": result
        }), 200
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": "Invalid request data", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Error generating post: {str(e)}")
        return jsonify({"error": "Failed to generate post", "message": str(e)}), 500

@post_bp.route('/suggest', methods=['GET'])
@login_required
@async_route
@log_api_errors
async def suggest_posts():
    """Get post suggestions based on preferences"""
    try:
        # Validate query parameters
        schema = SuggestPostSchema()
        data = schema.load(request.args.to_dict())
        
        # Check user permissions
        if not current_user.is_active:
            return jsonify({"error": "User account is not active"}), 403
        
        # Get post service
        post_service = get_post_service()
        
        # Get preferences
        preferences = {
            "topics": data['topics'],
            "tone": data['tone'],
            "industry": data['industry'],
            "user_id": current_user.id
        }
        
        # Get suggestions
        suggestions = await post_service.get_post_suggestions(
            preferences=preferences,
            count=data['count']
        )
        
        return jsonify({
            "success": True,
            "data": suggestions
        }), 200
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": "Invalid request parameters", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Error getting post suggestions: {str(e)}")
        return jsonify({"error": "Failed to get suggestions", "message": str(e)}), 500

@post_bp.route('/serial', methods=['POST'])
@login_required
@async_route
@log_api_errors
async def create_serial_content():
    """Create serial/episodic content"""
    try:
        # Validate request data
        schema = SerialContentSchema()
        data = schema.load(request.json)
        
        # Check user permissions
        if not current_user.is_active:
            return jsonify({"error": "User account is not active"}), 403
        
        # Get post service
        post_service = get_post_service()
        
        # Create serial content
        result = await post_service.create_serial_content(
            topic=data['topic'],
            parts=data['parts'],
            content_type=data['content_type'],
            metadata=data['metadata']
        )
        
        return jsonify({
            "success": True,
            "data": result
        }), 200
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": "Invalid request data", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating serial content: {str(e)}")
        return jsonify({"error": "Failed to create serial content", "message": str(e)}), 500

@post_bp.route('/campaign', methods=['POST'])
@login_required
@async_route
@log_api_errors
async def create_campaign():
    """Create a campaign with multiple scheduled posts"""
    try:
        # Validate request data
        schema = CampaignSchema()
        data = schema.load(request.json)
        
        # Check user permissions
        if not current_user.is_active:
            return jsonify({"error": "User account is not active"}), 403
            
        # Validate that posts and schedule have the same length
        if len(data['posts']) != len(data['schedule']):
            return jsonify({
                "error": "Invalid request data", 
                "message": "Number of posts must match number of scheduled times"
            }), 400
        
        # Get post service
        post_service = get_post_service()
        
        # Prepare posts and schedule
        posts = data['posts']
        schedule = [item['datetime'] for item in data['schedule']]
        
        # Create campaign
        result = await post_service.create_campaign(
            name=data['name'],
            posts=posts,
            schedule=schedule
        )
        
        return jsonify({
            "success": True,
            "data": result
        }), 200
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": "Invalid request data", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating campaign: {str(e)}")
        return jsonify({"error": "Failed to create campaign", "message": str(e)}), 500

@post_bp.route('/retry/<int:post_id>', methods=['POST'])
@login_required
@async_route
@log_api_errors
async def retry_post(post_id):
    """Retry a failed post"""
    try:
        # Check user permissions
        if not current_user.is_active:
            return jsonify({"error": "User account is not active"}), 403
        
        # Get post service
        post_service = get_post_service()
        
        # Retry post
        result = await post_service.retry_failed_post(post_id)
        
        return jsonify({
            "success": True,
            "data": result
        }), 200
        
    except ValueError as e:
        logger.warning(f"Invalid retry request: {str(e)}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error retrying post: {str(e)}")
        return jsonify({"error": "Failed to retry post", "message": str(e)}), 500

@post_bp.route('/list', methods=['GET'])
@login_required
def list_posts():
    """Get all posts for the current user with filtering and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        platform = request.args.get('platform')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Get post service
        post_service = get_post_service()
        
        # Get posts with filters and pagination
        result = post_service.get_user_posts(
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            platform=platform,
            date_from=date_from,
            date_to=date_to
        )
        
        return jsonify({
            "success": True,
            "data": result["posts"],
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": result["total"],
                "pages": result["pages"]
            }
        }), 200
    except ValueError as e:
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error retrieving posts: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve posts"}), 500

@post_bp.route('/<int:post_id>', methods=['DELETE'])
@login_required
def delete_post(post_id):
    """Delete a post"""
    try:
        # Get post service
        post_service = get_post_service()
        
        # Delete post
        result = post_service.delete_post(post_id, current_user.id)
        
        if result:
            return jsonify({
                "success": True,
                "message": "Post deleted successfully"
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Post not found or you don't have permission to delete it"
            }), 404
            
    except Exception as e:
        logger.error(f"Error deleting post: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to delete post",
            "message": str(e)
        }), 500
