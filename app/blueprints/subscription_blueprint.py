from flask import Blueprint, request, jsonify, current_app, redirect, url_for
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
from datetime import datetime
import logging
from ..services.subscription_service import SubscriptionService
from ..models.user import UserRole

logger = logging.getLogger(__name__)
subscription_bp = Blueprint('subscription', __name__, url_prefix='/api/subscriptions')
subscription_service = SubscriptionService()

# Validation schemas
class CreateSubscriptionSchema(Schema):
    plan_name = fields.String(required=True)
    start_date = fields.DateTime(required=True)
    end_date = fields.DateTime(required=True)
    posts_limit = fields.Integer(missing=100)
    accounts_limit = fields.Integer(missing=5)  # Changed from channels_limit
    agents_limit = fields.Integer(missing=3)

class UpdateSubscriptionSchema(Schema):
    plan_name = fields.String(missing=None)
    start_date = fields.DateTime(missing=None)
    end_date = fields.DateTime(missing=None)
    posts_limit = fields.Integer(missing=None)
    accounts_limit = fields.Integer(missing=None)  # Changed from channels_limit
    agents_limit = fields.Integer(missing=None)

def admin_required(f):
    """Decorator to check if user is an admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('admin.login', next=request.url))
        if current_user.role != UserRole.ADMIN:
            return jsonify({"success": False, "error": "Admin privileges required"}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@subscription_bp.route('/create', methods=['POST'])
@login_required
def create_subscription():
    """Create a new subscription for the current user"""
    try:
        # Validate request data
        schema = CreateSubscriptionSchema()
        data = schema.load(request.json)
        
        # Create subscription
        subscription = subscription_service.create_subscription(
            user_id=str(current_user.id),
            plan_name=data['plan_name'],
            start_date=data['start_date'],
            end_date=data['end_date'],
            posts_limit=data['posts_limit'],
            accounts_limit=data['accounts_limit'],  # Changed from channels_limit
            agents_limit=data['agents_limit']
        )
        
        return jsonify({
            "success": True,
            "data": subscription.to_mongo()
        }), 201
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to create subscription"}), 500

@subscription_bp.route('/', methods=['GET'])
@login_required
def get_current_subscription():
    """Get the current user's active subscription"""
    try:
        subscription = subscription_service.get_subscription(str(current_user.id))
        
        if not subscription:
            return jsonify({
                "success": False, 
                "error": "No active subscription found"
            }), 404
            
        return jsonify({
            "success": True,
            "data": subscription.to_mongo()
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve subscription"}), 500

@subscription_bp.route('/update/<subscription_id>', methods=['PUT'])
@login_required
def update_subscription(subscription_id):
    """Update an existing subscription"""
    try:
        # Validate request data
        schema = UpdateSubscriptionSchema()
        data = schema.load(request.json)
        
        # Check if subscription exists and belongs to current user
        subscription = subscription_service.get_subscription(str(current_user.id))
        if not subscription or str(subscription.id) != subscription_id:
            return jsonify({
                "success": False, 
                "error": "Subscription not found or does not belong to current user"
            }), 404
        
        # Update subscription
        updated_subscription = subscription_service.update_subscription(
            subscription_id=subscription_id,
            plan_name=data.get('plan_name'),
            start_date=data.get('start_date'),
            end_date=data.get('end_date'),
            posts_limit=data.get('posts_limit'),
            accounts_limit=data.get('accounts_limit'),  # Changed from channels_limit
            agents_limit=data.get('agents_limit')
        )
        
        if not updated_subscription:
            return jsonify({
                "success": False, 
                "error": "Failed to update subscription"
            }), 500
            
        return jsonify({
            "success": True,
            "data": updated_subscription.to_mongo()
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error updating subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to update subscription"}), 500

@subscription_bp.route('/cancel/<subscription_id>', methods=['POST'])
@login_required
def cancel_subscription(subscription_id):
    """Cancel a subscription"""
    try:
        # Check if subscription exists and belongs to current user
        subscription = subscription_service.get_subscription(str(current_user.id))
        if not subscription or str(subscription.id) != subscription_id:
            return jsonify({
                "success": False, 
                "error": "Subscription not found or does not belong to current user"
            }), 404
        
        # Cancel subscription
        success = subscription_service.cancel_subscription(subscription_id)
        
        if not success:
            return jsonify({
                "success": False, 
                "error": "Failed to cancel subscription"
            }), 500
            
        return jsonify({
            "success": True,
            "message": "Subscription canceled successfully"
        }), 200
    except Exception as e:
        logger.error(f"Error canceling subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to cancel subscription"}), 500

@subscription_bp.route('/all', methods=['GET'])
@admin_required
def get_all_subscriptions():
    """Get all subscriptions (admin only)"""
    try:
        subscriptions = subscription_service.get_all_subscriptions()
        
        return jsonify({
            "success": True,
            "data": [sub.to_mongo() for sub in subscriptions]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving all subscriptions: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve subscriptions"}), 500





