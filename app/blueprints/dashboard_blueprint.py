from flask import Blueprint, render_template, redirect, url_for, session, current_app, request, flash
from flask_login import login_required, current_user
from ..services.dashboard_service import DashboardService
from ..models import Strategy, Post, Comment, Analytics, Account  # Changed Channel to Account
import logging
from datetime import datetime, timedelta

# Make sure the blueprint is defined with the correct name and URL prefix
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')
logger = logging.getLogger(__name__)

# Initialize the services
dashboard_service = DashboardService()

@dashboard_bp.route('/')
@login_required
def index():
    """Display the main dashboard."""
    try:
        # Get language from session
        lang = session.get('lang', 'en')
        
        # Get active strategy
        active_strategy = Strategy.objects(user=current_user.id, is_active=True).first()
        
        # Get recent posts
        recent_posts = Post.objects(user=current_user.id).order_by('-created_at').limit(5)
        
        # Get recent comments
        recent_comments = Comment.objects(user=current_user.id).order_by('-created_time').limit(10)
        
        # Get analytics data
        analytics_data = Analytics.objects(post__in=recent_posts)
        
        # Get accounts
        accounts = Account.objects(user=current_user.id)  # Changed from channels to accounts
        
        # Add suggested replies to each comment
        for comment in recent_comments:
            try:
                comment.suggested_replies = current_app.ai_provider_manager.execute_with_failover(
                    "generate_quick_replies",
                    args=(comment.message, active_strategy if active_strategy else None)
                )
            except Exception as e:
                logger.error(f"Error generating quick replies for comment {comment.id}: {str(e)}")
                comment.suggested_replies = ["Thank you for your feedback!", "We appreciate your input!"]
        
        # Get post suggestions from LLM service
        try:
            post_suggestions = []
            if active_strategy:
                suggestions = current_app.ai_provider_manager.execute_with_failover(
                    "generate_post_suggestions",
                    args=(active_strategy,)
                )
                
                # Format suggestions properly
                for suggestion in suggestions:
                    if isinstance(suggestion, str):
                        post_suggestions.append({"message": suggestion})
                    elif isinstance(suggestion, dict) and "message" in suggestion:
                        post_suggestions.append(suggestion)
        except Exception as e:
            logger.error(f"Error generating post suggestions: {str(e)}")
            post_suggestions = []
        
        # Calculate stats for today's posts
        today = datetime.now().date()
        today_posts = Post.objects(
            user=current_user.id, 
            created_at__gte=datetime.combine(today, datetime.min.time())
        ).count()

        # Calculate stats
        stats = {
            'total_posts': Post.objects(user=current_user.id).count(),
            'total_comments': Comment.objects(user=current_user.id).count(),
            'total_accounts': accounts.count(),
            'today_posts': today_posts,
            'engagement_metrics': dashboard_service.calculate_engagement_metrics(recent_posts, analytics_data),
            'sentiment_summary': {
                'positive': 0,
                'negative': 0,
                'neutral': 0
            }
        }
        
        # Get activity summary
        activity_summary = {
            'activities': []
        }

        # Add recent post activities
        for post in recent_posts[:3]:  # Get up to 3 recent posts
            created_time = post.created_at if hasattr(post, 'created_at') else datetime.now()
            time_diff = datetime.now() - created_time
            
            if time_diff.days > 0:
                time_str = f"{time_diff.days} روز پیش"
            elif time_diff.seconds // 3600 > 0:
                time_str = f"{time_diff.seconds // 3600} ساعت پیش"
            else:
                time_str = f"{time_diff.seconds // 60} دقیقه پیش"
            
            activity_summary['activities'].append({
                'icon': 'edit',
                'color': 'primary',
                'text': 'پست جدید ایجاد شد',
                'time': time_str
            })

        # Add recent comment activities
        for comment in recent_comments[:3]:  # Get up to 3 recent comments
            created_time = comment.created_time if hasattr(comment, 'created_time') else datetime.now()
            time_diff = datetime.now() - created_time
            
            if time_diff.days > 0:
                time_str = f"{time_diff.days} روز پیش"
            elif time_diff.seconds // 3600 > 0:
                time_str = f"{time_diff.seconds // 3600} ساعت پیش"
            else:
                time_str = f"{time_diff.seconds // 60} دقیقه پیش"
            
            activity_summary['activities'].append({
                'icon': 'comment',
                'color': 'success',
                'text': 'کامنت جدید دریافت شد',
                'time': time_str
            })

        # Get feedback summary
        feedback_summary = dashboard_service.analyze_feedback(recent_comments)
        
        # Add today's date for the calendar
        today = datetime.now().date()
        
        # Make sure activity_summary is properly structured
        activity_summary = {
            'activities': [
                {
                    'icon': 'edit',
                    'color': 'primary',
                    'text': 'پست جدید ایجاد شد',
                    'time': '10 دقیقه پیش'
                },
                # More activities...
            ]
        }
        
        return render_template(
            'dashboard/index.html',
            lang=lang,
            active_strategy=active_strategy,
            posts=recent_posts, 
            stats=stats,
            recent_comments=recent_comments,
            accounts=accounts,  # Changed from channels to accounts
            post_suggestions=post_suggestions,
            activity_summary=activity_summary,
            feedback_summary=feedback_summary,
            today=today,  # Pass today to the template
            timedelta=timedelta  # Pass timedelta to the template
        )
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        # Return a simpler template for error cases
        return render_template(
            'dashboard/index.html',
            lang=session.get('lang', 'en'),
            active_strategy=None,
            posts=[], 
            stats={
                'total_posts': 0,
                'total_comments': 0,
                'total_accounts': 0,  # Changed from total_channels to total_accounts
                'engagement_metrics': {'likes': 0, 'comments': 0, 'shares': 0},
                'sentiment_summary': {
                    'positive': 0,
                    'negative': 0,
                    'neutral': 0
                }
            },
            recent_comments=[],
            accounts=[],  # Changed from channels to accounts
            post_suggestions=[],
            activity_summary="Unable to load activity summary.",
            feedback_summary={"summary": "Unable to load feedback analysis.", "topics": []}
        )

@dashboard_bp.route('/analytics')
@login_required
def analytics():
    """Display analytics dashboard."""
    try:
        # Get posts with analytics data
        posts = Post.objects(user=current_user.id)
        analytics_data = Analytics.objects(post__in=posts)
        
        # Process analytics data
        processed_data = dashboard_service.process_analytics_data(analytics_data)
        
        return render_template(
            'dashboard/analytics.html',
            analytics=processed_data
        )
    except Exception as e:
        logger.error(f"Error loading analytics: {str(e)}")
        return render_template('dashboard/analytics.html', analytics={})

@dashboard_bp.route('/posts/list', methods=['GET'])
@login_required
def posts_list():
    """Display a list of all posts with filtering options."""
    # Get language from session
    lang = session.get('lang', 'en')
    
    # Get available platforms for filter dropdown
    available_platforms = current_app.social_connector_manager.get_available_platforms()
    
    return render_template('dashboard/posts/list.html', 
                          lang=lang,
                          platforms=available_platforms)

# Existing routes
@dashboard_bp.route('/posts/advanced', methods=['GET', 'POST'])
@login_required
def create_post():
    """Create a new post with platform-specific content."""
    if request.method == 'POST':
        # Get common data
        platform = request.form.get('platform')
        creation_mode = request.form.get('creation_mode')
        
        if not platform:
            flash('Please select a platform', 'error')
            return redirect(url_for('dashboard.create_post'))
        
        # Get platform-specific content
        content = None
        if platform == 'instagram':
            content = request.form.get('instagram_caption')
            hashtags = request.form.get('instagram_hashtags')
            if hashtags:
                content = f"{content}\n\n{hashtags}"
        elif platform == 'telegram':
            content = request.form.get('telegram_text')
            # Handle buttons if needed
        elif platform == 'blog':
            title = request.form.get('blog_title')
            content = request.form.get('blog_content')
            tags = request.form.get('blog_tags')
            # Create blog post with title and tags
        
        if not content:
            flash('Content is required', 'error')
            return redirect(url_for('dashboard.create_post'))
            
        # Create post logic
        try:
            new_post = Post(
                user=current_user.id,
                content=content,
                platform=platform,
                created_at=datetime.now()
            )
            new_post.save()
            flash('Post created successfully!', 'success')
            return redirect(url_for('dashboard.index'))
        except Exception as e:
            logger.error(f"Error creating post: {str(e)}")
            flash('Error creating post. Please try again.', 'error')
    
    return render_template('dashboard/posts/create.html')

@dashboard_bp.route('/posts/edit/<int:post_id>', methods=['GET', 'POST'])
@login_required
def edit_post(post_id):
    """Edit an existing post."""
    try:
        # Get the post
        post = Post.objects(id=post_id, user=current_user.id).first()
        
        if not post:
            flash('Post not found', 'error')
            return redirect(url_for('dashboard.index'))
            
        if request.method == 'POST':
            content = request.form.get('content')
            if not content:
                flash('Post content is required', 'error')
                return redirect(url_for('dashboard.edit_post', post_id=post_id))
                
            # Update post logic
            post.content = content
            post.updated_at = datetime.now()
            post.save()
            
            flash('Post updated successfully!', 'success')
            return redirect(url_for('dashboard.index'))
        
        return render_template('dashboard/posts/edit.html', post=post)
    except Exception as e:
        logger.error(f"Error editing post: {str(e)}")
        flash('Error editing post. Please try again.', 'error')
        return redirect(url_for('dashboard.index'))

@dashboard_bp.route('/posts/quick', methods=['GET', 'POST'])
@login_required
def quick_post():
    """Create a quick post with minimal options."""
    if request.method == 'POST':
        # Handle quick post submission
        content = request.form.get('content')
        platform = request.form.get('platform')
        
        if not content:
            flash('Content is required', 'error')
            return redirect(url_for('dashboard.quick_post'))
            
        # Process quick post
        # ...
        
        flash('Post created successfully!', 'success')
        return redirect(url_for('dashboard.index'))
        
    return render_template('dashboard/posts/quick.html')

@dashboard_bp.route('/test')
def test():
    """Test route to verify the dashboard blueprint is working."""
    return "Dashboard blueprint is working!"

@dashboard_bp.route('/settings/profile', methods=['GET', 'POST'])
@login_required
def profile_settings():
    """User profile settings page"""
    if request.method == 'POST':
        # Handle form submission
        name = request.form.get('name')
        email = request.form.get('email')
        
        # Update user information
        current_user.name = name
        current_user.email = email
        current_user.save()
        
        flash('Profile updated successfully', 'success')
        return redirect(url_for('dashboard.profile_settings'))
    
    return render_template('dashboard/settings/profile.html')

# Add other routes as needed...



