from flask import Blueprint, request, jsonify, current_app, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
from ..services.account_service import AccountService  # Changed from channel_service
from ..models.account import Platform  # Changed from channel to account

logger = logging.getLogger(__name__)
account_bp = Blueprint('account', __name__, url_prefix='/dashboard/accounts')  # Changed from /api/accounts

# Validation schemas
class CreateAccountSchema(Schema):
    platform = fields.String(required=True)
    account_name = fields.String(required=True)
    account_identifier = fields.String(required=True)
    agent_id = fields.String(missing=None)

class UpdateAccountSchema(Schema):
    platform = fields.String(missing=None)
    account_name = fields.String(missing=None)
    account_identifier = fields.String(missing=None)
    agent_id = fields.String(missing=None)

@account_bp.route('/', methods=['GET'])
@login_required
def get_all_accounts():
    """Get all accounts for the current user"""
    try:
        accounts = AccountService.get_all_accounts(str(current_user.id))
        
        return jsonify({
            "success": True,
            "data": [account.to_mongo() for account in accounts]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving accounts: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve accounts"}), 500

@account_bp.route('/<account_id>', methods=['GET'])
@login_required
def get_account(account_id):
    """Get a specific account by ID"""
    try:
        account = AccountService.get_account(account_id)
        if not account:
            return jsonify({"success": False, "error": "Account not found"}), 404
            
        # Check if account belongs to current user
        if str(account.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
            
        return jsonify({
            "success": True,
            "data": account.to_mongo()
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving account: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve account"}), 500

@account_bp.route('/', methods=['POST'])
@login_required
def create_account():
    """Create a new account"""
    try:
        # Validate request data
        schema = CreateAccountSchema()
        data = schema.load(request.json)
        
        # Validate platform
        if data['platform'] not in [p.value for p in Platform]:
            return jsonify({
                "success": False, 
                "error": f"Invalid platform. Must be one of: {', '.join([p.value for p in Platform])}"
            }), 400
        
        # Create account
        account = AccountService.create_account(
            user_id=str(current_user.id),
            platform=data['platform'],
            account_name=data['account_name'],
            account_identifier=data['account_identifier'],
            agent_id=data.get('agent_id')
        )
        
        return jsonify({
            "success": True,
            "data": account.to_mongo()
        }), 201
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except ValueError as e:
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        return jsonify({"success": False, "error": "Failed to create account"}), 500

@account_bp.route('/<account_id>', methods=['PUT'])
@login_required
def update_account(account_id):
    """Update an existing account"""
    try:
        # Check if account exists and belongs to current user
        account = AccountService.get_account(account_id)
        if not account:
            return jsonify({"success": False, "error": "Account not found"}), 404
            
        if str(account.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Validate request data
        schema = UpdateAccountSchema()
        data = schema.load(request.json)
        
        # Validate platform if provided
        if data.get('platform') and data['platform'] not in [p.value for p in Platform]:
            return jsonify({
                "success": False, 
                "error": f"Invalid platform. Must be one of: {', '.join([p.value for p in Platform])}"
            }), 400
        
        # Update account
        updated_account = AccountService.update_account(
            account_id=account_id,
            platform=data.get('platform'),
            account_name=data.get('account_name'),
            account_identifier=data.get('account_identifier'),
            agent_id=data.get('agent_id')
        )
        
        if not updated_account:
            return jsonify({
                "success": False, 
                "error": "Failed to update account"
            }), 500
        
        return jsonify({
            "success": True,
            "data": updated_account.to_mongo()
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except ValueError as e:
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error updating account: {str(e)}")
        return jsonify({"success": False, "error": "Failed to update account"}), 500

@account_bp.route('/<account_id>/disconnect', methods=['POST'])
@login_required
def disconnect_account(account_id):
    """Disconnect an account from the web interface"""
    try:
        # Check if account exists and belongs to current user
        account = AccountService.get_account(account_id)
        if not account:
            flash("Account not found", "error")
            return redirect(url_for('account.index'))
            
        if str(account.user.id) != str(current_user.id):
            flash("Unauthorized", "error")
            return redirect(url_for('account.index'))
        
        # Disconnect account
        success = AccountService.disconnect_account(account_id)
        
        if not success:
            flash("Failed to disconnect account", "error")
        else:
            flash("Account disconnected successfully", "success")
            
        return redirect(url_for('account.index'))
    except Exception as e:
        logger.error(f"Error disconnecting account: {str(e)}")
        flash("An error occurred while disconnecting the account", "error")
        return redirect(url_for('account.index'))

# Add these routes for the web interface
@account_bp.route('/manage', methods=['GET'])
@login_required
def index():
    """Display the account management page"""
    try:
        accounts = AccountService.get_all_accounts(str(current_user.id))
        return render_template(
            'dashboard/accounts/index.html',
            accounts=accounts,
            platforms=[p.value for p in Platform]
        )
    except Exception as e:
        logger.error(f"Error retrieving accounts: {str(e)}")
        flash("Failed to retrieve accounts", "error")
        return redirect(url_for('dashboard.index'))

index.endpoint = 'account.index'  # Explicitly set the endpoint name

@account_bp.route('/connect', methods=['GET', 'POST'])
@login_required
def connect():
    """Connect a new social media account"""
    if request.method == 'POST':
        platform = request.form.get('platform')
        account_name = request.form.get('account_name')
        account_identifier = request.form.get('account_identifier')
        
        try:
            account = AccountService.create_account(
                user_id=str(current_user.id),
                platform=platform,
                account_name=account_name,
                account_identifier=account_identifier
            )
            flash("Account connected successfully", "success")
            return redirect(url_for('account.index'))
        except Exception as e:
            logger.error(f"Error connecting account: {str(e)}")
            flash("Failed to connect account", "error")
    
    # For GET requests, generate a new Telegram verification code if requested
    platform = request.args.get('platform')
    verification_code = None
    
    if platform == 'telegram':
        try:
            verification_code = AccountService.generate_verification_code(
                user_id=current_user.id,
                platform='telegram'
            )
        except Exception as e:
            logger.error(f"Error generating verification code: {str(e)}")
            flash("Failed to generate verification code", "error")
    
    return render_template('dashboard/accounts/connect.html', 
                          verification_code=verification_code,
                          platform=platform)

@account_bp.route('/generate-verification-code', methods=['POST'])
@login_required
def generate_verification_code():
    """Generate a verification code for connecting a social platform"""
    platform = request.json.get('platform')
    
    if not platform:
        return jsonify({"success": False, "error": "Platform is required"}), 400
        
    try:
        code = AccountService.generate_verification_code(
            user_id=current_user.id,
            platform=platform
        )
        return jsonify({
            "success": True,
            "code": code
        })
    except Exception as e:
        logger.error(f"Error generating verification code: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

