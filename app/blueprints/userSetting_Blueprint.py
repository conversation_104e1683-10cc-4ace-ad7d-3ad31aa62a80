from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, session
from flask_login import login_required, current_user
from ..models import Account, Platform, CommentSetting  # Changed Channel to Account
from datetime import datetime

# Change the blueprint name to match the URL prefix
user_settings_bp = Blueprint('user_settings', __name__, url_prefix='/dashboard/settings')

@user_settings_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile settings page"""
    if request.method == 'POST':
        # Handle form submission
        name = request.form.get('name')
        email = request.form.get('email')
        
        # Update user information
        current_user.name = name
        current_user.email = email
        current_user.save()
        
        flash('Profile updated successfully', 'success')
        return redirect(url_for('user_settings.profile'))
    
    return render_template('dashboard/settings/profile.html')

@user_settings_bp.route('/templates', methods=['GET', 'POST'])
@login_required
def templates():
    account = Account.objects(user=current_user, platform=Platform.FACEBOOK).first()  # Changed Channel to Account
    
    if request.method == 'POST':
        template = request.form.get('template')
        if not account:  # Changed channel to account
            flash('Please set up your page profile first')
            return redirect(url_for('user_settings.profile'))
        
        if template:
            if 'response_templates' not in account.metadata:  # Changed channel to account
                account.metadata['response_templates'] = []  # Changed channel to account
            account.metadata['response_templates'].append(template)  # Changed channel to account
            account.save()  # Changed channel to account
            flash('Response template added successfully!')
    
    templates = account.metadata.get('response_templates', []) if account else []  # Changed channel to account
    return render_template('dashboard/settings/templates.html', templates=templates)

@user_settings_bp.route('/templates/generate', methods=['POST'])
@login_required
def generate_template():
    from flask import current_app
    
    scenario = request.json.get('scenario')
    tone = request.json.get('tone', 'professional')
    account = Account.objects(user=current_user, platform=Platform.FACEBOOK).first()  # Changed Channel to Account
    
    if account and 'metadata' in account:  # Already updated in previous edit
        context = f"Category: {account.metadata.get('category', '')}\nTarget Audience: {account.metadata.get('target_audience', '')}"
        
        # Replace llm_service.generate_response with ai_provider_manager
        template = current_app.ai_provider_manager.execute_with_failover(
            "generate_text",
            args=(f"Generate a response template for {scenario} with {tone} tone. {context}",),
            kwargs={"max_tokens": 200}
        ).get('text', '')
        
        return jsonify({'template': template})
    
    return jsonify({'error': 'Profile not found'}), 404

@user_settings_bp.route('/auto-reply', methods=['GET', 'POST'])
@login_required
def auto_reply_settings():
    # Use CommentSetting instead of AutoReplySettings
    settings = CommentSetting.objects(user=current_user).first()
    
    if not settings:
        settings = CommentSetting(user=current_user).save()
    
    if request.method == 'POST':
        settings.enabled = 'enabled' in request.form
        settings.reply_to_all = 'reply_to_all' in request.form
        settings.reply_to_negative = 'reply_to_negative' in request.form
        settings.reply_template = request.form.get('reply_template', '')
        settings.updated_at = datetime.utcnow()
        settings.save()
        
        flash('Auto-reply settings updated successfully')
        return redirect(url_for('user_settings.auto_reply_settings'))
    
    return render_template('dashboard/settings/auto_reply.html', settings=settings)



















