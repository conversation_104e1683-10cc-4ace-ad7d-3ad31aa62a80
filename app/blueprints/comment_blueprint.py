from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from ..integrations.social.facebook_connector import FacebookConnector
from ..integrations.ai.openai_provider import OpenAIProvider
from ..services.dashboard_service import DashboardService
from ..models import Account, Platform, Comment  # Changed Channel to Account
import logging

comments_bp = Blueprint('comments', __name__, url_prefix='/dashboard/comments')
facebook_connector = FacebookConnector({"access_token": "", "page_id": ""})
ai_provider = OpenAIProvider()
dashboard_service = DashboardService(facebook_connector, ai_provider)
logger = logging.getLogger(__name__)

# Add monitor route from comments_blueprint.py
@comments_bp.route('/monitor')
@login_required
def monitor():
    """Display the comments monitoring dashboard."""
    try:
        # Get comments for the current user
        comments = Comment.objects(user=current_user.id).order_by('-created_time')
        
        return render_template(
            'dashboard/comments/monitor.html',
            comments=comments,
            lang=session.get('lang', 'en')
        )
    except Exception as e:
        logger.error(f"Error loading comments monitor: {str(e)}")
        return render_template(
            'dashboard/comments/monitor.html',
            comments=[],
            lang=session.get('lang', 'en')
        )

# Add reply route from comments_blueprint.py
@comments_bp.route('/<comment_id>/reply', methods=['POST'])
@login_required
def reply(comment_id):
    """Reply to a comment."""
    try:
        data = request.get_json()
        message = data.get('message', '')
        
        # Get the comment
        comment = Comment.objects(id=comment_id, user=current_user.id).first()
        
        if not comment:
            return jsonify({"error": "Comment not found"}), 404
            
        # Create reply logic here
        # This is a placeholder - implement your actual reply logic
        reply_id = "new_reply_id"  # This would be the ID of the newly created reply
        
        return jsonify({"id": reply_id, "success": True})
    except Exception as e:
        logger.error(f"Error replying to comment: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Add auto-reply route from comments_blueprint.py
@comments_bp.route('/auto-reply/<comment_id>', methods=['POST'])
@login_required
def auto_reply(comment_id):
    comment_text = request.json.get('comment')
    account = Account.objects(user=current_user, platform=Platform.FACEBOOK).first()  # Changed Channel to Account
    
    if not account:  # Changed channel to account
        return jsonify({'error': 'Account not found'}), 404  # Changed Channel to Account
    
    context = f"""
    Page Category: {account.metadata.get('category', '')}  # Changed channel to account
    Target Audience: {account.metadata.get('target_audience', '')}  # Changed channel to account
    Language: {account.metadata.get('content_language', 'en')}  # Changed channel to account
    """
    
    messages = [{"role": "user", "content": f"Context: {context}\n\nPlease generate a response to this comment: {comment_text}"}]
    response = ai_provider.chat_with_gpt(messages)["text"]
    result = facebook_connector.post_comment(comment_id, response)
    
    return jsonify({
        'reply': response,
        'result': result
    })

@comments_bp.route('/quick_reply', methods=['POST'])
@login_required
def quick_reply():
    """Handle quick reply to comments from the dashboard."""
    comment_id = request.form.get('comment_id')
    reply_message = request.form.get('reply_message')
    
    if not comment_id or not reply_message:
        flash('Comment ID and reply message are required.', 'error')
        return redirect(url_for('dashboard.index'))
    
    result = dashboard_service.quick_reply_to_comment(comment_id, reply_message)
    
    if 'error' in result:
        flash(f'Error replying to comment: {result["error"]}', 'error')
    else:
        flash('Reply sent successfully!', 'success')
    
    return redirect(url_for('dashboard.index'))

# Add API endpoint for comments
@comments_bp.route('/api/reply', methods=['POST'])
@login_required
def api_reply():
    """API endpoint for replying to comments."""
    data = request.json
    comment_id = data.get('comment_id')
    message = data.get('message')
    
    if not comment_id or not message:
        return jsonify({'success': False, 'error': 'Comment ID and message are required'})
    
    result = facebook_connector.reply_to_comment(comment_id, message)
    
    if 'error' in result:
        return jsonify({'success': False, 'error': result['error']})
    
    return jsonify({'success': True, 'data': result})

@comments_bp.route('/negative')
@login_required
def negative_comments():
    posts = facebook_connector.get_page_posts()
    negative_comments = []
    
    for post in posts.get('data', []):
        comments = facebook_connector.get_post_comments(post['id'])
        for comment in comments.get('data', []):
            sentiment = ai_provider.analyze_sentiment(comment['message'])
            if sentiment['sentiment'] == 'negative':
                comment['sentiment'] = sentiment
                comment['post_message'] = post.get('message', '')[:100] + '...'
                negative_comments.append(comment)
    
    return render_template('comments/negative.html', comments=negative_comments)




