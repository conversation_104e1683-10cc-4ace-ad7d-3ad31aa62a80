from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
from ..services.agent_service import AgentService
from ..models.agent import AgentType

logger = logging.getLogger(__name__)
agent_bp = Blueprint('agent', __name__, url_prefix='/api/agent')
agent_service = AgentService()

# Validation schemas
class CreateAgentSchema(Schema):
    name = fields.String(required=True)
    agent_type = fields.String(required=True)
    description = fields.String(missing="")
    config = fields.Dict(missing=dict)

class UpdateAgentSchema(Schema):
    name = fields.String(missing=None)
    description = fields.String(missing=None)
    config = fields.Dict(missing=None)

@agent_bp.route('/', methods=['GET'])
@login_required
def get_agents():
    """Get all agents for the current user"""
    try:
        agents = agent_service.get_agents(str(current_user.id))
        return jsonify({
            "success": True,
            "data": [agent.to_mongo() for agent in agents]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving agents: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve agents"}), 500

@agent_bp.route('/<agent_id>', methods=['GET'])
@login_required
def get_agent(agent_id):
    """Get a specific agent by ID"""
    try:
        agent = agent_service.get_agent(agent_id)
        if not agent:
            return jsonify({"success": False, "error": "Agent not found"}), 404
            
        # Check if agent belongs to current user
        if str(agent.user) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
            
        return jsonify({
            "success": True,
            "data": agent.to_mongo()
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving agent: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve agent"}), 500

@agent_bp.route('/', methods=['POST'])
@login_required
def create_agent():
    """Create a new agent"""
    try:
        # Validate request data
        schema = CreateAgentSchema()
        data = schema.load(request.json)
        
        # Validate agent type
        if data['agent_type'] not in [t.value for t in AgentType]:
            return jsonify({
                "success": False, 
                "error": f"Invalid agent type. Must be one of: {', '.join([t.value for t in AgentType])}"
            }), 400
        
        # Create agent
        agent = agent_service.create_agent(
            user_id=str(current_user.id),
            name=data['name'],
            agent_type=data['agent_type'],
            description=data['description'],
            config=data['config']
        )
        
        return jsonify({
            "success": True,
            "data": agent.to_mongo()
        }), 201
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}")
        return jsonify({"success": False, "error": "Failed to create agent"}), 500

@agent_bp.route('/<agent_id>', methods=['PUT'])
@login_required
def update_agent(agent_id):
    """Update an existing agent"""
    try:
        # Check if agent exists and belongs to current user
        agent = agent_service.get_agent(agent_id)
        if not agent:
            return jsonify({"success": False, "error": "Agent not found"}), 404
            
        if str(agent.user) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Validate request data
        schema = UpdateAgentSchema()
        data = schema.load(request.json)
        
        # Filter out None values
        update_data = {k: v for k, v in data.items() if v is not None}
        
        # Update agent
        updated_agent = agent_service.update_agent(agent_id, **update_data)
        
        return jsonify({
            "success": True,
            "data": updated_agent.to_mongo()
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error updating agent: {str(e)}")
        return jsonify({"success": False, "error": "Failed to update agent"}), 500

@agent_bp.route('/<agent_id>/activate', methods=['POST'])
@login_required
def activate_agent(agent_id):
    """Activate an agent"""
    try:
        # Check if agent exists and belongs to current user
        agent = agent_service.get_agent(agent_id)
        if not agent:
            return jsonify({"success": False, "error": "Agent not found"}), 404
            
        if str(agent.user) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Activate agent
        success = agent_service.activate_agent(agent_id)
        
        if success:
            return jsonify({"success": True}), 200
        else:
            return jsonify({"success": False, "error": "Failed to activate agent"}), 500
    except Exception as e:
        logger.error(f"Error activating agent: {str(e)}")
        return jsonify({"success": False, "error": "Failed to activate agent"}), 500

@agent_bp.route('/<agent_id>/deactivate', methods=['POST'])
@login_required
def deactivate_agent(agent_id):
    """Deactivate an agent"""
    try:
        # Check if agent exists and belongs to current user
        agent = agent_service.get_agent(agent_id)
        if not agent:
            return jsonify({"success": False, "error": "Agent not found"}), 404
            
        if str(agent.user) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Deactivate agent
        success = agent_service.deactivate_agent(agent_id)
        
        if success:
            return jsonify({"success": True}), 200
        else:
            return jsonify({"success": False, "error": "Failed to deactivate agent"}), 500
    except Exception as e:
        logger.error(f"Error deactivating agent: {str(e)}")
        return jsonify({"success": False, "error": "Failed to deactivate agent"}), 500