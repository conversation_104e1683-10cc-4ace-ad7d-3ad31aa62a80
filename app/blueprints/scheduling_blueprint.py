from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
from datetime import datetime
from typing import Dict, Any
import logging
from functools import wraps
import asyncio

from ..models.post_schedule import RecurrenceType
# Remove the direct import of SchedulingService
# from ..services.scheduling_service import SchedulingService
from ..integrations.ai.base_ai_provider import BaseAIProvider
from ..integrations.social.base_social_connector import BaseSocialIntegration as BaseSocialConnector

logger = logging.getLogger(__name__)

# Create blueprint
scheduling_bp = Blueprint('scheduling', __name__, url_prefix='/api/scheduling')

# Helper to run async functions in Flask
def async_route(f):
    @wraps(f)
    def wrapped(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapped

# Get SchedulingService instance
def get_scheduling_service():
    # Import here to avoid circular imports
    from ..services.scheduling_service import SchedulingService
    
    # Get AI provider manager and social connector manager from app context
    ai_provider_manager = current_app.ai_provider_manager
    social_connector_manager = current_app.social_connector_manager
    
    return SchedulingService(
        ai_provider_manager=ai_provider_manager,
        social_connector_manager=social_connector_manager
    )

@scheduling_bp.route('/schedule', methods=['POST'])
@login_required
def schedule_post():
    try:
        data = request.json
        # Validate input data
        # ...
        
        # Get scheduling service
        scheduling_service = get_scheduling_service()
        
        # Schedule the post
        result = scheduling_service.schedule_post(
            user=current_user,
            content=data.get('content'),
            scheduled_time=datetime.fromisoformat(data.get('scheduled_time')),
            platform_name=data.get('platform')
        )
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        logger.error(f"Error scheduling post: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# Add more routes as needed

