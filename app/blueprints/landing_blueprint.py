from flask import Blueprint, render_template, request, session, redirect, url_for

landing_bp = Blueprint('landing', __name__, url_prefix='')

@landing_bp.route('/')
def index():
    """Display the landing page."""
    # Get language from session or default to Persian
    lang = session.get('lang', 'fa')
    return render_template('landing/index.html', lang=lang)

@landing_bp.route('/switch-language/<lang>')
def switch_language(lang):
    """Switch between languages."""
    session['lang'] = lang
    return redirect(request.referrer or url_for('landing.index'))

@landing_bp.route('/pricing')
def pricing():
    """Display the pricing page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/pricing.html', lang=lang)

@landing_bp.route('/about')
def about():
    """Display the about us page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/about.html', lang=lang)

@landing_bp.route('/blog')
@landing_bp.route('/blog/')
@landing_bp.route('/blog/<int:page>')
def blog(page=1):
    """Display the blog page."""
    lang = session.get('lang', 'fa')
    
    # Import models and services
    from ..models import Blog
    
    # Get published blogs with pagination
    per_page = 9  # 3x3 grid
    blogs = Blog.objects(is_published=True).order_by('-published_at').skip((page-1)*per_page).limit(per_page)
    
    # Get total count for pagination
    total = Blog.objects(is_published=True).count()
    pages = (total + per_page - 1) // per_page  # Ceiling division
    
    return render_template('landing/blog.html', 
                          lang=lang,
                          blogs=blogs,
                          page=page,
                          pages=pages)

@landing_bp.route('/help')
def help():
    """Display the help page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/help.html', lang=lang)

@landing_bp.route('/terms')
def terms():
    """Display the terms and privacy page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/terms.html', lang=lang)

@landing_bp.route('/contact')
def contact():
    """Display the contact us page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/contact.html', lang=lang)

@landing_bp.route('/faq')
def faq():
    """Display the FAQ page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/faq.html', lang=lang)

# Features routes moved from features.py
@landing_bp.route('/features/')
@landing_bp.route('/features')
def features():
    """Display the main features page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/features.html', lang=lang)

@landing_bp.route('/features/content')
def features_content():
    """Display the content creation features page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/features_content.html', lang=lang)

@landing_bp.route('/features/analytics')
def features_analytics():
    """Display the analytics features page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/features_analytics.html', lang=lang)

@landing_bp.route('/features/automation')
def features_automation():
    """Display the automation features page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/features_automation.html', lang=lang)

@landing_bp.route('/features')
def features_index():
    """Display the features index page."""
    lang = session.get('lang', 'fa')
    return render_template('landing/features_index.html', lang=lang)
