from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from ..models import Strategy  # Updated import
# Remove this import: from ..services.llm_service import LLMService

# Update URL prefix to be under dashboard
strategy_bp = Blueprint('strategy', __name__, url_prefix='/dashboard/strategy')
# Remove this line: llm_service = LLMService()

@strategy_bp.route('/')
@login_required
def index():
    """List all strategies."""
    strategies = Strategy.objects(user=current_user).order_by('-is_active')
    # Get language from session
    lang = session.get('lang', 'en')
    # Update template path
    return render_template('dashboard/strategy/index.html', strategies=strategies, lang=lang)

@strategy_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create a new strategy."""
    if request.method == 'POST':
        # Process form data here
        name = request.form.get('name')
        description = request.form.get('description')
        business_type = request.form.get('business_type')
        tone_of_voice = request.form.get('tone_of_voice')
        is_active = request.form.get('is_active') == 'on'
        
        # Create strategy object
        strategy = Strategy(
            user=current_user,
            name=name,
            description=description,
            business_type=business_type,
            tone_of_voice=tone_of_voice,
            is_active=is_active
        )
        
        # Save strategy
        strategy.save()
        
        flash('Strategy created successfully!')
        return redirect(url_for('strategy.index'))
            
    # Update template path
    return render_template('dashboard/strategy/create.html')

@strategy_bp.route('/<strategy_id>/activate', methods=['POST'])
@login_required
def activate(strategy_id):
    """Activate a specific strategy."""
    try:
        strategy = Strategy.objects.get(id=strategy_id, user=current_user.id)
        
        # Deactivate all other strategies
        Strategy.objects(user=current_user.id).update(is_active=False)
        
        # Activate selected strategy
        strategy.is_active = True
        strategy.save()
        
        # Update LLM service with new strategy
        llm_service.set_strategy(strategy)
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
























