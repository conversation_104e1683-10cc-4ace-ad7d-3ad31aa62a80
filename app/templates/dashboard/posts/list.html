{% extends "dashboard/dashboard_layout.html" %}

{% block title %}مدیریت پست‌ها - Rominext{% endblock %}

{% block dashboard_content %}
<style>
  /* Updated card styling */
  .post-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border-radius: 10px;
    overflow: hidden;
  }
  
  .post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  
  /* Platform-specific card backgrounds */
  .post-card.instagram-card {
    background-color: rgba(225, 48, 108, 0.05);
  }
  
  .post-card.telegram-card {
    background-color: rgba(0, 136, 204, 0.05);
  }
  
  .post-card.twitter-card {
    background-color: rgba(29, 161, 242, 0.05);
  }
  
  .post-card.blog-card {
    background-color: rgba(255, 87, 34, 0.05);
  }
  
  /* Updated platform headers */
  .post-card .card-header {
    padding: 0.75rem 1rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: transparent;
  }
  
  .platform-badge {
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, 0.7);
  }
</style>
<div class="posts-management-dashboard">
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="card-title m-0 fw-bold">مدیریت پست‌ ها</h5>
        <div class="d-flex gap-2">
          <div class="btn-group" role="group" aria-label="View mode">
            <button type="button" class="btn btn-outline-secondary active" id="cardViewBtn">
              <i class="fas fa-th-large"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" id="listViewBtn">
              <i class="fas fa-list"></i>
            </button>
          </div>
          <a href="{{ url_for('dashboard.quick_post') }}" class="btn btn-success">
            <i class="fas fa-plus ms-1"></i> ایجاد پست جدید
          </a>
        </div>
      </div>
    </div>
    <div class="card-body">
      <!-- Filters -->
      <div class="row g-3 align-items-end mb-4">
        <div class="col-md-2">
          <label class="form-label fw-bold">پلتفرم</label>
          <select id="platformFilter" class="form-select">
            <option value="all">همه پلتفرم‌ها</option>
            <option value="instagram">اینستاگرام</option>
            <option value="telegram">تلگرام</option>
            <option value="twitter">توییتر</option>
            <option value="blog">بلاگ</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">از تاریخ</label>
          <div class="input-group">
            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
            <input type="date" id="dateFromFilter" class="form-control">
          </div>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">تا تاریخ</label>
          <div class="input-group">
            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
            <input type="date" id="dateToFilter" class="form-control">
          </div>
        </div>
        <div class="col-md-4 d-flex gap-2">
          <button id="resetFilters" class="btn btn-outline-secondary w-50" title="حذف فیلترها">
            <i class="fas fa-undo me-1"></i> حذف فیلترها
          </button>
          <button id="applyFilters" class="btn btn-primary w-50" title="اعمال فیلتر">
            <i class="fas fa-filter me-1"></i> اعمال فیلتر
          </button>
        </div>
      </div>

      

      <!-- Posts Grid -->
      <div class="row g-4" id="postsContainer">
        <!-- Posts will be loaded here -->
      </div>

      <!-- Posts List (initially hidden) -->
      <div class="table-responsive" id="postsListContainer" style="display: none;">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>پلتفرم</th>
              <th>محتوا</th>
              <th>تاریخ</th>
              <th>وضعیت</th>
              <th>تعامل</th>
              <th>عملیات</th>
            </tr>
          </thead>
          <tbody id="postsListBody">
            <!-- List items will be loaded here -->
          </tbody>
          <tfoot id="postsListEmpty" style="display: none;">
            <tr>
              <td colspan="6" class="text-center py-5">
                <div class="empty-state-container">
                  <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                  <h5 class="text-muted">هیچ پستی یافت نشد</h5>
                  <p class="text-muted" id="emptyListMessage">پست جدیدی ایجاد کنید یا فیلترهای خود را تغییر دهید.</p>
                  <a href="{{ url_for('dashboard.quick_post') }}" class="btn btn-success mt-3">
                    <i class="fas fa-plus ms-1"></i> ایجاد پست جدید
                  </a>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Empty State -->
      <div id="emptyState" class="text-center py-5" style="display: none;">
        <div class="empty-state-container">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">هیچ پستی یافت نشد</h5>
          <p class="text-muted">پست جدیدی ایجاد کنید یا فیلترهای خود را تغییر دهید.</p>
        </div>
      </div>

      <!-- Loading State -->
      <div id="loadingState" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">در حال بارگذاری...</span>
        </div>
        <p class="mt-2 text-muted">در حال بارگذاری پست‌ها...</p>
      </div>

      <!-- Pagination -->
      <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center" id="pagination">
          <!-- Pagination will be generated here -->
        </ul>
      </nav>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Elements
  const postsContainer = document.getElementById('postsContainer');
  const postsListContainer = document.getElementById('postsListContainer');
  const postsListBody = document.getElementById('postsListBody');
  const postsListEmpty = document.getElementById('postsListEmpty');
  const emptyState = document.getElementById('emptyState');
  const emptyListMessage = document.getElementById('emptyListMessage');
  const loadingState = document.getElementById('loadingState');
  const pagination = document.getElementById('pagination');
  const platformFilter = document.getElementById('platformFilter');
  const dateFromFilter = document.getElementById('dateFromFilter');
  const dateToFilter = document.getElementById('dateToFilter');
  const applyFiltersBtn = document.getElementById('applyFilters');
  const resetFiltersBtn = document.getElementById('resetFilters');
  const cardViewBtn = document.getElementById('cardViewBtn');
  const listViewBtn = document.getElementById('listViewBtn');

  // State
  let currentPage = 1;
  let totalPages = 1;
  let currentView = 'card'; // 'card' or 'list'
  let filters = {
    platform: 'all',
    dateFrom: '',
    dateTo: ''
  };

  // Initialize
  loadPosts();

  // View toggle event listeners
  cardViewBtn.addEventListener('click', function() {
    if (currentView !== 'card') {
      currentView = 'card';
      cardViewBtn.classList.add('active');
      listViewBtn.classList.remove('active');
      
      // Check if we have posts
      const posts = filterPosts(getMockPosts());
      if (posts.length === 0) {
        postsListContainer.style.display = 'none';
        emptyState.style.display = 'block';
      } else {
        postsContainer.style.display = 'flex';
        postsListContainer.style.display = 'none';
      }
    }
  });

  listViewBtn.addEventListener('click', function() {
    if (currentView !== 'list') {
      currentView = 'list';
      listViewBtn.classList.add('active');
      cardViewBtn.classList.remove('active');
      
      // Check if we have posts
      const posts = filterPosts(getMockPosts());
      if (posts.length === 0) {
        postsContainer.style.display = 'none';
        emptyState.style.display = 'none';
        postsListContainer.style.display = 'block';
        postsListBody.style.display = 'none';
        postsListEmpty.style.display = 'table-row-group';
      } else {
        postsContainer.style.display = 'none';
        postsListContainer.style.display = 'block';
        postsListBody.style.display = 'table-row-group';
        postsListEmpty.style.display = 'none';
      }
    }
  });

  // Event Listeners
  applyFiltersBtn.addEventListener('click', function() {
    filters.platform = platformFilter.value;
    filters.dateFrom = dateFromFilter.value;
    filters.dateTo = dateToFilter.value;
    currentPage = 1;
    loadPosts();
  });

  resetFiltersBtn.addEventListener('click', function() {
    platformFilter.value = 'all';
    dateFromFilter.value = '';
    dateToFilter.value = '';
    filters = {
      platform: 'all',
      dateFrom: '',
      dateTo: ''
    };
    currentPage = 1;
    loadPosts();
  });

  // Check if any filters are active
  function hasActiveFilters() {
    return filters.platform !== 'all' || filters.dateFrom !== '' || filters.dateTo !== '';
  }

  // Update empty state message based on filter status
  function updateEmptyStateMessage() {
    const emptyStateMessage = document.querySelector('#emptyState p');
    if (hasActiveFilters()) {
      emptyStateMessage.textContent = 'هیچ پستی با فیلترهای انتخاب شده یافت نشد. فیلترها را تغییر دهید.';
    } else {
      emptyStateMessage.textContent = 'پست جدیدی ایجاد کنید یا فیلترهای خود را تغییر دهید.';
    }
  }

  // Load posts function
  function loadPosts() {
    // Show loading state
    loadingState.style.display = 'block';
    postsContainer.style.display = 'none';
    postsListContainer.style.display = 'none';
    emptyState.style.display = 'none';
    
    // Build query parameters
    const queryParams = new URLSearchParams({
      page: currentPage,
      per_page: 10
    });
    
    if (filters.platform && filters.platform !== 'all') {
      queryParams.append('platform', filters.platform);
    }
    
    if (filters.dateFrom) {
      queryParams.append('date_from', filters.dateFrom);
    }
    
    if (filters.dateTo) {
      queryParams.append('date_to', filters.dateTo);
    }
    
    // Fetch posts from API
    fetch(`/posts/list?${queryParams.toString()}`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        // Hide loading state
        loadingState.style.display = 'none';
        
        if (data.success) {
          const posts = data.data;
          totalPages = data.pagination.pages;
          
          // Update pagination
          updatePagination();
          
          // Check if we have posts
          if (posts.length === 0) {
            if (currentView === 'card') {
              emptyState.style.display = 'block';
            } else {
              postsListContainer.style.display = 'block';
              postsListBody.style.display = 'none';
              postsListEmpty.style.display = 'table-row-group';
            }
          } else {
            if (currentView === 'card') {
              postsContainer.style.display = 'flex';
              renderCardView(posts);
            } else {
              postsListContainer.style.display = 'block';
              postsListBody.style.display = 'table-row-group';
              postsListEmpty.style.display = 'none';
              renderListView(posts);
            }
          }
        } else {
          // Show error message
          emptyState.style.display = 'block';
          emptyListMessage.textContent = 'خطا در بارگذاری پست‌ها. لطفا دوباره تلاش کنید.';
        }
      })
      .catch(error => {
        // Hide loading state
        loadingState.style.display = 'none';
        
        // Show error message
        emptyState.style.display = 'block';
        emptyListMessage.textContent = 'خطا در بارگذاری پست‌ها. لطفا دوباره تلاش کنید.';
        console.error('Error fetching posts:', error);
      });
  }

  // Render card view (original view)
  function renderCardView(posts) {
    postsContainer.innerHTML = '';
    
    posts.forEach(post => {
      const postElement = document.createElement('div');
      postElement.className = 'col-md-6 col-lg-4 mb-4';
      
      let platformIcon, platformName, platformClass;
      switch(post.platform) {
        case 'instagram':
          platformIcon = 'fa-instagram';
          platformName = 'اینستاگرام';
          platformClass = 'instagram-card';
          break;
        case 'telegram':
          platformIcon = 'fa-telegram-plane';
          platformName = 'تلگرام';
          platformClass = 'telegram-card';
          break;
        case 'twitter':
          platformIcon = 'fa-twitter';
          platformName = 'توییتر';
          platformClass = 'twitter-card';
          break;
        case 'blog':
          platformIcon = 'fa-rss';
          platformName = 'بلاگ';
          platformClass = 'blog-card';
          break;
        default:
          platformIcon = 'fa-globe';
          platformName = post.platform || 'سایر';
          platformClass = '';
      }
      
      // Determine status
      let statusClass, statusText;
      if (post.published) {
        statusClass = 'published';
        statusText = 'منتشر شده';
      } else if (post.scheduled) {
        statusClass = 'scheduled';
        statusText = 'زمانبندی شده';
      } else {
        statusClass = 'draft';
        statusText = 'پیش‌نویس';
      }
      
      // Format date (assuming post.created_at is in ISO format)
      const postDate = new Date(post.created_at);
      const formattedDate = new Intl.DateTimeFormat('fa-IR').format(postDate);
      
      postElement.innerHTML = `
        <div class="post-card ${platformClass}">
          <div class="card-header">
            <div class="platform-info">
              <div class="platform-icon">
                <i class="fab ${platformIcon}"></i>
              </div>
              <span>${platformName}</span>
            </div>
            <div class="post-actions">
              <button class="action-btn" onclick="event.stopPropagation(); document.getElementById('action-menu-${post.id}').classList.toggle('show');">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="action-menu" id="action-menu-${post.id}">
                <div class="action-item" onclick="editPost(${post.id})">
                  <i class="fas fa-edit text-primary"></i>
                  <span>ویرایش</span>
                </div>
                <div class="action-item" onclick="analyzePost(${post.id})">
                  <i class="fas fa-chart-line text-info"></i>
                  <span>آنالیز</span>
                </div>
                <div class="action-item" onclick="copyPost(${post.id})">
                  <i class="fas fa-copy text-secondary"></i>
                  <span>کپی</span>
                </div>
                <div class="action-item delete" onclick="confirmDelete(${post.id}, event)">
                  <i class="fas fa-trash-alt text-danger"></i>
                  <span>حذف</span>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="post-image">
              ${post.image ? 
                `<img src="${post.image}" alt="Post image">` : 
                `<i class="fas fa-image fa-2x text-muted"></i>`
              }
            </div>
            <p class="post-content">${post.content}</p>
            <div class="post-meta">
              <div class="post-date">
                <i class="far fa-calendar-alt me-1"></i>
                ${post.date}
              </div>
              <div class="post-status ${statusClass}">
                ${statusText}
              </div>
            </div>
          </div>
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="engagement-stats">
                <div class="stat-item">
                  <i class="fas fa-heart text-danger"></i>
                  <span>${post.likes}</span>
                </div>
                <div class="stat-item">
                  <i class="fas fa-comment text-primary"></i>
                  <span>${post.comments}</span>
                </div>
              </div>
              <button class="view-btn">
                <i class="fas fa-eye me-1"></i>
                مشاهده
              </button>
            </div>
          </div>
        </div>
      `;
      
      postsContainer.appendChild(postElement);
    });
  }

  // Toggle action menu
  function toggleActionMenu(event, postId) {
    event.stopPropagation();
    
    // Close all other menus
    document.querySelectorAll('.action-menu.show').forEach(menu => {
      if (menu.id !== `action-menu-${postId}`) {
        menu.classList.remove('show');
      }
    });
    
    // Toggle current menu
    const menu = document.getElementById(`action-menu-${postId}`);
    menu.classList.toggle('show');
  }

  // Close menus when clicking outside
  document.addEventListener('click', function() {
    document.querySelectorAll('.action-menu.show').forEach(menu => {
      menu.classList.remove('show');
    });
  });

  // Action functions
  function editPost(postId) {
    console.log(`Edit post ${postId}`);
    // Implementation here
  }

  function analyzePost(postId) {
    console.log(`Analyze post ${postId}`);
    // Implementation here
  }

  function copyPost(postId) {
    console.log(`Copy post ${postId}`);
    // Implementation here
  }

  function confirmDelete(postId, event) {
    event.stopPropagation();
    
    if (confirm('آیا از حذف این پست اطمینان دارید؟')) {
      // Call API to delete post
      fetch(`/posts/${postId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Reload posts after successful deletion
          loadPosts();
          // Show success message
          alert('پست با موفقیت حذف شد.');
        } else {
          alert(`خطا در حذف پست: ${data.error}`);
        }
      })
      .catch(error => {
        console.error('Error deleting post:', error);
        alert('خطا در حذف پست. لطفا دوباره تلاش کنید.');
      });
    }
  }

  // Render list view (new compact view)
  function renderListView(posts) {
    postsListBody.innerHTML = '';
    
    posts.forEach(post => {
      const row = document.createElement('tr');
      
      let platformIcon, platformClass, platformName;
      switch(post.platform) {
        case 'instagram':
          platformIcon = 'fa-instagram';
          platformClass = 'text-instagram';
          platformName = 'اینستاگرام';
          break;
        case 'telegram':
          platformIcon = 'fa-telegram-plane';
          platformClass = 'text-telegram';
          platformName = 'تلگرام';
          break;
        case 'twitter':
          platformIcon = 'fa-twitter';
          platformClass = 'text-twitter';
          platformName = 'توییتر';
          break;
        case 'blog':
          platformIcon = 'fa-rss';
          platformClass = 'text-blog';
          platformName = 'بلاگ';
          break;
      }
      
      row.innerHTML = `
        <td><i class="fab ${platformIcon} ${platformClass} me-1"></i> ${platformName}</td>
        <td class="text-truncate" style="max-width: 250px;">${post.content}</td>
        <td>${post.date}</td>
        <td>
          ${post.scheduled ? 
            `<span class="badge bg-warning text-dark"><i class="far fa-clock me-1"></i> زمان‌بندی شده</span>` : 
            post.published ? 
            `<span class="badge bg-success"><i class="fas fa-check me-1"></i> منتشر شده</span>` : 
            `<span class="badge bg-secondary"><i class="fas fa-pencil-alt me-1"></i> پیش‌نویس</span>`}
        </td>
        <td>
          <span class="me-2"><i class="fas fa-heart text-danger me-1"></i> ${post.likes}</span>
          <span><i class="fas fa-comment text-primary me-1"></i> ${post.comments}</span>
        </td>
        <td>
          <div class="btn-group">
            <button class="btn btn-sm btn-outline-primary">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2 text-primary"></i> ویرایش</a></li>
              <li><a class="dropdown-item" href="#"><i class="fas fa-chart-line me-2 text-info"></i> آنالیز</a></li>
              <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2 text-secondary"></i> کپی</a></li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item text-danger" href="#" onclick="confirmDelete(${post.id}, event)">
                  <i class="fas fa-trash-alt me-2"></i> حذف
                </a>
              </li>
            </ul>
          </div>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
  }

  // Render pagination
  function renderPagination() {
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Previous">
      <span aria-hidden="true">&laquo;</span>
    </a>`;
    prevLi.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage > 1) {
        currentPage--;
        loadPosts();
      }
    });
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
      const pageLi = document.createElement('li');
      pageLi.className = `page-item ${currentPage === i ? 'active' : ''}`;
      pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
      pageLi.addEventListener('click', function(e) {
        e.preventDefault();
        currentPage = i;
        loadPosts();
      });
      pagination.appendChild(pageLi);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Next">
      <span aria-hidden="true">&raquo;</span>
    </a>`;
    nextLi.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage < totalPages) {
        currentPage++;
        loadPosts();
      }
    });
    pagination.appendChild(nextLi);
  }

  // Filter posts
  function filterPosts(posts) {
    return posts.filter(post => {
      // Platform filter
      if (filters.platform !== 'all' && post.platform !== filters.platform) {
        return false;
      }
      
      // Date from filter
      if (filters.dateFrom && new Date(post.date) < new Date(filters.dateFrom)) {
        return false;
      }
      
      // Date to filter
      if (filters.dateTo && new Date(post.date) > new Date(filters.dateTo)) {
        return false;
      }
      
      return true;
    });
  }

  // Delete confirmation
  window.confirmDelete = function(postId, event) {
    event.preventDefault();
    if (confirm('آیا از حذف این پست اطمینان دارید؟')) {
      // Call delete API
      console.log('Deleting post with ID:', postId);
      // After successful deletion, reload posts
      loadPosts();
    }
  };

  // Mock data function - replace with actual API call
  function getMockPosts() {
    totalPages = 3; // Set total pages
    
    // For testing empty state, uncomment the next line
    // return [];
    
    return [
      {
        id: 1,
        platform: 'instagram',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. #محتوا #هوش_مصنوعی',
        image: 'https://via.placeholder.com/300',
        date: '1402/02/15',
        likes: 120,
        comments: 14,
        published: true,
        scheduled: false
      },
      {
        id: 2,
        platform: 'telegram',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
        image: null,
        date: '1402/02/18',
        likes: 45,
        comments: 8,
        published: false,
        scheduled: true
      },
      {
        id: 3,
        platform: 'instagram',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. #رومینکست',
        image: 'https://via.placeholder.com/300',
        date: '1402/02/20',
        likes: 89,
        comments: 5,
        published: true,
        scheduled: false
      },
      {
        id: 4,
        platform: 'twitter',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ.',
        image: null,
        date: '1402/02/22',
        likes: 32,
        comments: 3,
        published: true,
        scheduled: false
      },
      {
        id: 5,
        platform: 'blog',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است.',
        image: 'https://via.placeholder.com/300',
        date: '1402/02/25',
        likes: 15,
        comments: 2,
        published: false,
        scheduled: false
      },
      {
        id: 6,
        platform: 'telegram',
        content: 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
        image: 'https://via.placeholder.com/300',
        date: '1402/02/28',
        likes: 67,
        comments: 9,
        published: true,
        scheduled: false
      }
    ];
  }
});
</script>

<style>
  /* Card base styling */
  .post-card {
    @apply rounded-xl shadow-sm border-0 overflow-hidden transition-all duration-300 ease-in-out;
    background-color: white;
  }
  
  .post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  
  /* Platform-specific styling */
  .post-card.instagram-card .card-header {
    background-color: rgba(225, 48, 108, 0.1);
  }
  
  .post-card.telegram-card .card-header {
    background-color: rgba(0, 136, 204, 0.1);
  }
  
  .post-card.twitter-card .card-header {
    background-color: rgba(29, 161, 242, 0.1);
  }
  
  .post-card.blog-card .card-header {
    background-color: rgba(255, 87, 34, 0.1);
  }
  
  /* Card header styling */
  .post-card .card-header {
    padding: 0.75rem 1rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    margin: 0.75rem;
  }
  
  .platform-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }
  
  .platform-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
  
  /* Action menu styling */
  .post-actions {
    position: relative;
  }
  
  .action-btn {
    background: transparent;
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .action-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 160px;
    z-index: 9999;
    overflow: visible;
    display: none;
  }
  
  .action-menu.show {
    display: block;
  }
  
  .action-item {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .action-item:hover {
    background-color: #f8f9fa;
  }
  
  .action-item i {
    width: 20px;
    margin-left: 0.75rem;
    text-align: center;
  }
  
  .action-item.delete:hover {
    background-color: #fff5f5;
  }
  
  /* Card body styling */
  .post-card .card-body {
    padding: 1rem;
  }
  
  .post-image {
    height: 160px;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
  }
  
  .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .post-content {
    margin-bottom: 1rem;
    line-height: 1.5;
    max-height: 4.5em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  
  .post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .post-date {
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  .post-status {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-published {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
  }
  
  .status-scheduled {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }
  
  .status-draft {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
  
  /* Card footer styling */
  .post-card .card-footer {
    padding: 1rem;
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .engagement-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  .view-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    background-color: transparent;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }
  
  .view-btn:hover {
    background-color: #f8f9fa;
    border-color: #ced4da;
  }
</style>
{% endblock %}
