{% extends "dashboard/dashboard_layout.html" %}

{% block title %}Account Management - Rominext{% endblock %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h2>مدیریت حساب‌های کاربری</h2>
            <a href="{{ url_for('account.connect') }}" class="btn btn-success rounded-pill">
                <i class="fas fa-plus me-2"></i> اتصال حساب جدید
            </a>
        </div>
    </div>
    
    <div class="col-12">
        {% if accounts %}
            <div class="row">
                {% for account in accounts %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="account-card {% if account.platform == 'telegram' %}telegram-card{% endif %}">
                        <div class="platform-badge">
                            {% if account.platform == 'telegram' %}
                            <i class="fab fa-telegram"></i>
                            {% else %}
                            <i class="fas fa-arrow-up-right-from-square"></i>
                            {% endif %}
                            <span class="connection-status">CONNECTED</span>
                        </div>
                        
                        <div class="account-content">
                            <h5 class="account-name">{{ account.platform|title }} Account</h5>
                            <div class="account-id">{{ account.account_identifier }}</div>
                            
                            <div class="account-stats">
                                <div class="stat-badge">
                                    <i class="fas fa-users"></i>
                                    <span>{{ account.followers|default('0') }} دنبال‌کننده</span>
                                </div>
                                <div class="stat-badge">
                                    <i class="fas fa-eye"></i>
                                    <span>{{ account.subscribers|default('0') }} مشترک</span>
                                </div>
                            </div>
                            
                            <div class="account-meta">
                                متصل شده در: {{ account.created_at.strftime('%Y-%m-%d') }}
                            </div>
                            
                            <div class="account-actions">
                                <form action="{{ url_for('account.disconnect_account', account_id=account.id) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید که می‌خواهید این حساب را قطع کنید؟');">
                                    <button type="submit" class="btn-icon btn-disconnect" data-bs-toggle="tooltip" data-bs-placement="top" title="قطع اتصال">
                                        <i class="fas fa-unlink"></i>
                                    </button>
                                </form>
                                <button class="btn-icon btn-delete" data-bs-toggle="tooltip" data-bs-placement="top" title="حذف">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <button class="btn-icon btn-edit" data-bs-toggle="tooltip" data-bs-placement="top" title="ویرایش">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <h4>هیچ حساب کاربری متصل نیست</h4>
                <p>برای شروع، حساب‌های شبکه‌های اجتماعی خود را به رومینکست متصل کنید.</p>
                <a href="{{ url_for('account.connect') }}" class="btn btn-success rounded-pill mt-3">
                    <i class="fas fa-plus me-2"></i> اتصال حساب جدید
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
    /* Account Card Styling */
    .account-card {
        background-color: #e5f7ff; /* Light blue background */
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        padding: 1.25rem;
        position: relative;
        overflow: hidden;
        height: 100%;
        transition: all 0.3s ease;
    }
    
    /* Platform Badge with Connection Status */
    .platform-badge {
        position: absolute;
        top: 1rem;
        left: 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .platform-badge i {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #0088cc;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }
    
    .connection-status {
        font-size: 0.7rem;
        font-weight: 600;
        color: #28a745;
        text-transform: uppercase;
        margin-top: 0.2rem;
    }
    
    /* Account Content */
    .account-content {
        padding-left: 3.5rem;
    }
    
    /* Account Name */
    .account-name {
        font-weight: 600;
        font-size: 1.25rem;
        color: #333;
        margin-bottom: 0.3rem;
        text-align: right;
    }
    
    /* Account ID */
    .account-id {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 1rem;
        text-align: right;
    }
    
    /* Account Stats */
    .account-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        gap: 0.5rem;
    }
    
    .stat-badge {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        background-color: rgba(0,0,0,0.05);
        border-radius: 8px;
        font-size: 0.85rem;
        color: #495057;
        flex: 1;
    }
    
    .stat-badge i {
        margin-left: 0.5rem;
        color: #6c757d;
    }
    
    /* Account Meta */
    .account-meta {
        color: #6c757d;
        font-size: 0.8rem;
        text-align: right;
        margin-bottom: 1rem;
    }
    
    /* Account Actions */
    .account-actions {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .btn-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-edit {
        color: #0088cc;
        border-color: #0088cc;
    }
    
    .btn-delete {
        color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-disconnect {
        color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-icon:hover {
        transform: translateY(-2px);
    }
    
    .btn-edit:hover {
        background-color: #0088cc;
        color: white;
    }
    
    .btn-delete:hover, .btn-disconnect:hover {
        background-color: #dc3545;
        color: white;
    }
    
    /* Empty State */
    .empty-state {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        padding: 3rem;
        text-align: center;
    }
    
    .empty-icon {
        font-size: 3rem;
        color: #adb5bd;
        margin-bottom: 1rem;
    }
</style>

<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
