{% extends "dashboard/dashboard_layout.html" %}

{% block title %}Connect Account - Rominext{% endblock %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2>اتصال حساب جدید</h2>
        <p class="text-muted">حساب شبکه اجتماعی خود را به رومینکست متصل کنید تا بتوانید محتوا را مدیریت کنید.</p>
    </div>
    
    <div class="col-md-12">
         <!-- Security Info Section -->
         <div class="card mt-4 mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2 text-success"></i>اطلاعات امنیتی</h5>
            </div>
            <div class="card-body">
                <p>رومینکست برای اتصال به حساب‌های شبکه‌های اجتماعی از API‌های رسمی استفاده می‌کند:</p>
                <ul>
                    <li>هرگز رمز عبور شما ذخیره نمی‌شود</li>
                    <li>اتصال از طریق پروتکل‌های امن و استاندارد انجام می‌شود</li>
                    <li>دسترسی‌ها محدود به موارد مورد نیاز برای مدیریت محتوا است</li>
                    <li>می‌توانید در هر زمان دسترسی رومینکست را لغو کنید</li>
                </ul>
                <p class="mb-0 text-muted"><small>برای اطلاعات بیشتر درباره نحوه اتصال امن، به <a href="{{ url_for('landing.help') }}">صفحه راهنما</a> مراجعه کنید.</small></p>
            </div>
        </div>
        <div class="row">
            <!-- Instagram Card -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center p-4">
                        <i class="fab fa-instagram text-danger mb-3" style="font-size: 3rem;"></i>
                        <h4>Instagram</h4>
                        <p class="text-muted">مدیریت پست‌ها، استوری‌ها و تعامل با فالوورهای خود</p>
                        <div class="mt-4">
                            <a href="#" class="btn btn-outline-danger connect-btn" data-platform="instagram">
                                <i class="fas fa-plug me-2"></i>اتصال به اینستاگرام
                            </a>
                        </div>
                        <p class="mt-3 mb-0"><small class="text-muted"><i class="fas fa-shield-alt me-1"></i>از طریق API رسمی، بدون نیاز به رمز عبور</small></p>
                    </div>
                </div>
            </div>
            
            <!-- Telegram Card -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center p-4">
                        <i class="fab fa-telegram text-info mb-3" style="font-size: 3rem;"></i>
                        <h4>Telegram</h4>
                        <p class="text-muted">مدیریت کانال‌ها، گروه‌ها و ارسال پیام‌های خودکار</p>
                        <div class="mt-4">
                            <a href="#" class="btn btn-outline-info connect-btn" data-platform="telegram">
                                <i class="fas fa-plug me-2"></i>اتصال به تلگرام
                            </a>
                        </div>
                        <p class="mt-3 mb-0"><small class="text-muted"><i class="fas fa-shield-alt me-1"></i>از طریق Bot API، بدون نیاز به رمز عبور</small></p>
                    </div>
                </div>
            </div>
            
            <!-- WhatsApp Card -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center p-4">
                        <i class="fab fa-whatsapp text-success mb-3" style="font-size: 3rem;"></i>
                        <h4>WhatsApp</h4>
                        <p class="text-muted">مدیریت پیام‌ها، گروه‌ها و ارسال پیام‌های خودکار</p>
                        <div class="mt-4">
                            <a href="#" class="btn btn-outline-success connect-btn" data-platform="whatsapp">
                                <i class="fas fa-plug me-2"></i>اتصال به واتس‌اپ
                            </a>
                        </div>
                        <p class="mt-3 mb-0"><small class="text-muted"><i class="fas fa-shield-alt me-1"></i>از طریق Business API، بدون نیاز به رمز عبور</small></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Telegram Connection Guide -->
        <div id="telegram-guide" class="row mt-4 {% if platform != 'telegram' %}d-none{% endif %}">
            <div class="col-12">
                <div class="card shadow-sm border-0 rounded-3">
                    <div class="card-header bg-info bg-opacity-10 border-0">
                        <h4 class="mb-0 py-2 text-info"><i class="fab fa-telegram me-2"></i> اتصال تلگرام</h4>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-4 mb-md-0">
                                <div class="connection-steps">
                                    <h5 class="mb-3"><i class="fas fa-list-ol text-secondary me-2"></i> مراحل اتصال</h5>
                                    <div class="steps-container">
                                        <div class="step d-flex align-items-start mb-3">
                                            <div class="step-number">1</div>
                                            <div class="step-content">تلگرام را باز کرده و به کانال یا گروه مورد نظر بروید.</div>
                                        </div>
                                        <div class="step d-flex align-items-start mb-3">
                                            <div class="step-number">2</div>
                                            <div class="step-content">ربات <strong>@rominext_bot</strong> را به عنوان ادمین با دسترسی انتشار پیام اضافه کنید.</div>
                                        </div>
                                        <div class="step d-flex align-items-start mb-3">
                                            <div class="step-number">3</div>
                                            <div class="step-content">یک پیام از کانال یا گروه خود را برای ربات فوروارد کنید.</div>
                                        </div>
                                        <div class="step d-flex align-items-start mb-3">
                                            <div class="step-number">4</div>
                                            <div class="step-content">کد احراز هویت را به ربات ارسال کنید.</div>
                                        </div>
                                        <div class="step d-flex align-items-start">
                                            <div class="step-number">5</div>
                                            <div class="step-content">پس از تأیید، کانال در داشبورد شما نمایش داده خواهد شد.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="verification-code-container text-center p-4 bg-light rounded-3">
                                    <h5 class="mb-3"><i class="fas fa-key text-info me-2"></i> کد احراز هویت</h5>
                                    <p>این کد را به ربات <strong>@rominext_bot</strong> ارسال کنید:</p>
                                    <div class="verification-code bg-white p-3 rounded-3 mb-3 border">
                                        <h3 class="mb-0 text-info">{{ verification_code }}</h3>
                                    </div>
                                    <a href="https://t.me/rominext_bot" target="_blank" class="btn btn-info btn-lg w-100">
                                        <i class="fab fa-telegram me-2"></i> ورود به ربات تلگرام
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info bg-info bg-opacity-10 border-0 mt-4">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-shield-alt text-info fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="text-info">امنیت و حریم خصوصی</h5>
                                    <p class="mb-0">رومینکست فقط از API رسمی تلگرام استفاده می‌کند. هیچ رمز عبور یا اطلاعات شخصی ذخیره نمی‌شود و شما می‌توانید در هر زمان با حذف ربات از کانال، دسترسی را لغو کنید.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}
.connect-btn {
    border-radius: 50px;
    padding: 8px 20px;
    transition: all 0.3s ease;
}
.connect-btn:hover {
    transform: scale(1.05);
}
/* New styles for the redesigned telegram guide */
.step-number {
    width: 30px;
    height: 30px;
    background-color: #0dcaf0;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}
.step-content {
    padding-top: 3px;
    margin-right: 10px; /* Add margin between number and text */
}
.verification-code-container {
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}
.verification-code {
    letter-spacing: 2px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const connectButtons = document.querySelectorAll('.connect-btn');
    
    connectButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const platform = this.getAttribute('data-platform');
            
            // Show platform-specific form or guide
            if (platform === 'telegram') {
                // Redirect to the same page with platform parameter to generate a new code
                window.location.href = "{{ url_for('account.connect') }}?platform=telegram";
            } else {
                document.getElementById('platform').value = platform;
                document.getElementById('connection-form').classList.remove('d-none');
                document.getElementById('connection-form').scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});
</script>

{% endblock %}





