{% extends "main_layout.html" %}

{% block title %}{{ t('auth.forgot_password') }} - Rominext{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-5">
        <div class="card shadow">
            <div class="card-header bg-white text-center py-3">
                <h4 class="mb-0 text-success">{{ t('auth.forgot_password') }}</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST">
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ t('auth.email') }}</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">{{ t('auth.reset_password') }}</button>
                    </div>
                </form>
                <p class="mt-3 text-center">
                    <a href="{{ url_for('vault.login') }}" class="text-success">{{ t('auth.back_to_login') }}</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
