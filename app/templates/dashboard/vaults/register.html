{% extends "main_layout.html" %}

{% block title %}{{ t('nav.signup') }} - Rominext{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="row">
                <!-- Form on the left -->
                <div class="col-md-5 order-md-1">
                    <h2 class="text-center mb-4 text-success fw-bold">{{ t('nav.signup') }}</h2>
                    
                    <form method="POST" class="mb-4">
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ t('auth.name') }}</label>
                            <input type="text" class="form-control form-control-md" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ t('auth.email') }}</label>
                            <input type="email" class="form-control form-control-md" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">{{ t('auth.password') }}</label>
                            <input type="password" class="form-control form-control-md" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">{{ t('auth.confirm_password') }}</label>
                            <input type="password" class="form-control form-control-md" id="confirm_password" name="confirm_password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">{{ t('auth.agree_terms') }}</label>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">{{ t('auth.create_account') }}</button>
                        </div>
                    </form>
                    
                    <div class="text-center">
                        <p>
                            {{ t('auth.have_account') }} <a href="{{ url_for('vault.login') }}" class="text-success">{{ t('nav.login') }}</a>
                        </p>
                    </div>
                </div>
                
                <!-- Description on the right, aligned with first input -->
                <div class="col-md-5 order-md-2 offset-md-1">
                    <hr class="d-md-none my-4">
                    
                    <div class="row" style="margin-top: 56px;">
                        <div class="col-12 mb-4">
                            <h5 class="mb-3"><i class="fas fa-lock text-success me-2"></i>{{ t('auth.terms_title') }}</h5>
                            <p class="text-muted">{{ t('auth.terms_section1_content') }}</p>
                        </div>
                        <div class="col-12">
                            <h5 class="mb-3"><i class="fas fa-rocket text-success me-2"></i>{{ t('auth.welcome_back') }}</h5>
                            <p class="text-muted">{{ t('auth.login_message') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

