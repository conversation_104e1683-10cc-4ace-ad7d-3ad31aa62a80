{% extends "main_layout.html" %}

{% block title %}{{ t('nav.login') }} - Rominext{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="row">
                <!-- Form on the left -->
                <div class="col-md-5 order-md-1">
                    <h2 class="text-center mb-4 text-success fw-bold">{{ t('nav.login') }}</h2>
             
                    
                    <form method="POST" class="mb-4">
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ t('auth.email') }}</label>
                            <input type="email" class="form-control form-control-md" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">{{ t('auth.password') }}</label>
                            <input type="password" class="form-control form-control-md" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">{{ t('auth.remember_me') }}</label>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">{{ t('nav.login') }}</button>
                        </div>
                    </form>
                    
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="{{ url_for('vault.forgot_password') }}" class="text-muted">{{ t('auth.forgot_password') }}</a>
                        </p>
                        <p>
                            {{ t('auth.no_account') }} <a href="{{ url_for('vault.register') }}" class="text-success">{{ t('nav.signup') }}</a>
                        </p>
                    </div>
                </div>
                
                <!-- Description on the right, aligned with first input -->
                <div class="col-md-5 order-md-2 offset-md-1">
                    <hr class="d-md-none my-4">
                    
                    <div class="row" style="margin-top: 56px;">
                        <div class="col-12 mb-4">
                            <h5 class="mb-3"><i class="fas fa-shield-alt text-success me-2"></i>{{ t('auth.secure_login_title') }}</h5>
                            <p class="text-muted">{{ t('auth.secure_login_desc') }}</p>
                        </div>
                        <div class="col-12">
                            <h5 class="mb-3"><i class="fas fa-question-circle text-success me-2"></i>{{ t('auth.login_help_title') }}</h5>
                            <p class="text-muted">{{ t('auth.login_help_desc') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

