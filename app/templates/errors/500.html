{% extends "main_layout.html" %}

{% block title %}{{ t('errors.server_error.title') }} - Rominext{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-body text-center p-5">
                    <i class="fas fa-exclamation-triangle text-danger mb-4" style="font-size: 3rem;"></i>
                    <h2 class="mb-3">{{ t('errors.server_error.title') }}</h2>
                    <p class="mb-4">{{ t('errors.server_error.message') }}</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ url_for('landing.index') }}" class="btn btn-primary">
                            <i class="fas fa-home {% if lang == 'fa' %}ms-2{% else %}me-2{% endif %}"></i>
                            {{ t('errors.server_error.home_button') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}