{% extends "main_layout.html" %}

{% block title %}{{ t('errors.not_found.title') }} - Rominext{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-body text-center p-5">
                    <i class="fas fa-map-signs text-warning mb-4" style="font-size: 3rem;"></i>
                    <h2 class="mb-3">{{ t('errors.not_found.title') }}</h2>
                    <p class="mb-4">{{ t('errors.not_found.message') }}</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ url_for('landing.index') }}" class="btn btn-primary">
                            <i class="fas fa-home {% if lang == 'fa' %}ms-2{% else %}me-2{% endif %}"></i>
                            {{ t('errors.not_found.home_button') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}