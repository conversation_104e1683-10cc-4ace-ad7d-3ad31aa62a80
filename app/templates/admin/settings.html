{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات سیستم - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header">
          <h5 class="card-title mb-0">تنظیمات سیستم</h5>
        </div>
        <div class="card-body">
          <form method="POST" action="{{ url_for('admin.update_settings') }}">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="site_name" class="form-label">نام سایت</label>
                <input type="text" class="form-control" id="site_name" name="site_name" value="Rominext">
              </div>
              <div class="col-md-6 mb-3">
                <label for="site_description" class="form-label">توضیحات سایت</label>
                <input type="text" class="form-control" id="site_description" name="site_description" value="سیستم مدیریت شبکه‌های اجتماعی">
              </div>
            </div>
            
            <h5 class="mt-4 mb-3">تنظیمات ایمیل</h5>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="smtp_host" class="form-label">SMTP Host</label>
                <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="smtp.example.com">
              </div>
              <div class="col-md-6 mb-3">
                <label for="smtp_port" class="form-label">SMTP Port</label>
                <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="587">
              </div>
              <div class="col-md-6 mb-3">
                <label for="smtp_user" class="form-label">SMTP Username</label>
                <input type="text" class="form-control" id="smtp_user" name="smtp_user" value="<EMAIL>">
              </div>
              <div class="col-md-6 mb-3">
                <label for="smtp_password" class="form-label">SMTP Password</label>
                <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="password">
              </div>
            </div>
            
            <h5 class="mt-4 mb-3">تنظیمات API</h5>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="facebook_api_key" class="form-label">Facebook API Key</label>
                <input type="text" class="form-control" id="facebook_api_key" name="facebook_api_key" value="">
              </div>
              <div class="col-md-6 mb-3">
                <label for="groq_api_key" class="form-label">Groq API Key</label>
                <input type="text" class="form-control" id="groq_api_key" name="groq_api_key" value="">
              </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
              <button type="submit" class="btn btn-primary">ذخیره تنظیمات</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}