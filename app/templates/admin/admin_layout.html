<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}پنل مدیریت - Rominext{% endblock %}</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/rtl.css') }}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    body {
      background-color: #f8f9fa;
    }
    
    .sidebar {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      width: 250px;
      background-color: #343a40;
      padding-top: 20px;
      color: white;
      z-index: 100;
    }
    
    .sidebar .nav-link {
      color: rgba(255, 255, 255, 0.8);
      padding: 10px 20px;
      margin-bottom: 5px;
    }
    
    .sidebar .nav-link:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link.active {
      color: white;
      background-color: #007bff;
    }
    
    .sidebar .nav-link i {
      margin-left: 10px;
    }
    
    .main-content {
      margin-right: 250px;
      padding: 20px;
    }
    
    .navbar {
      margin-right: 250px;
      background-color: white;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .user-dropdown img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
    
    .timeline {
      position: relative;
      padding-right: 45px;
    }
    
    .timeline-block {
      display: flex;
      position: relative;
    }
    
    .timeline-step {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      position: absolute;
      right: -45px;
    }
    
    .timeline-content {
      width: 100%;
      padding-right: 15px;
    }
    
    @media (max-width: 991.98px) {
      .sidebar {
        width: 100%;
        height: auto;
        position: relative;
      }
      
      .main-content, .navbar {
        margin-right: 0;
      }
    }
  </style>
  {% block extra_css %}{% endblock %}
</head>
<body>
  <!-- Sidebar -->
  <div class="sidebar">
    <div class="text-center mb-4">
      <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Rominext" class="img-fluid" style="max-width: 150px;">
      <h5 class="mt-3">پنل مدیریت</h5>
    </div>
    <ul class="nav flex-column">
      <li class="nav-item">
        <a class="nav-link {% if request.endpoint == 'admin.index' %}active{% endif %}" href="{{ url_for('admin.index') }}">
          <i class="fas fa-tachometer-alt"></i> داشبورد
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link {% if request.endpoint == 'admin.users' %}active{% endif %}" href="{{ url_for('admin.users') }}">
          <i class="fas fa-users"></i> کاربران
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link {% if request.endpoint == 'admin.posts' %}active{% endif %}" href="{{ url_for('admin.posts') }}">
          <i class="fas fa-file-alt me-2"></i>
          <span>پست‌ها</span>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#">
          <i class="fas fa-comments"></i> نظرات
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link {% if request.endpoint == 'admin.logs' %}active{% endif %}" href="{{ url_for('admin.logs') }}">
          <i class="fas fa-clipboard-list me-2"></i>
          <span>گزارش‌های سیستم</span>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#">
          <i class="fas fa-cog"></i> تنظیمات
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="{{ url_for('admin.logout') }}">
          <i class="fas fa-sign-out-alt"></i> خروج
        </a>
      </li>
    </ul>
  </div>

  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light">
    <div class="container-fluid">
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
          <li class="nav-item">
            <span class="nav-link">{{ current_user.name }} {% if current_user.is_admin %}(مدیر){% endif %}</span>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="main-content">
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    
    {% block content %}{% endblock %}
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  {% block extra_js %}{% endblock %}
</body>
</html>
