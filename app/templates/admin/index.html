{% extends "admin/admin_layout.html" %}

{% block title %}پنل مدیریت - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row">
    <div class="col-xl-3 col-sm-6 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">کاربران</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ users_count }}
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                <i class="fas fa-users text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">پست‌ها</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ posts_count }}
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                <i class="fas fa-file-alt text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">نظرات</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ comments_count }}
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                <i class="fas fa-comments text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">خطاها</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ errors_count }}
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                <i class="fas fa-exclamation-triangle text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  {% if error_message %}
  <div class="alert alert-danger">{{ error_message }}</div>
  {% endif %}

  <div class="row mt-4">
    <div class="col-lg-12 mb-lg-0 mb-4">
      <div class="card">
        <div class="card-header pb-0">
          <h6>فعالیت‌های اخیر</h6>
        </div>
        <div class="card-body p-3">
          <div class="timeline timeline-one-side">
            {% if recent_activities %}
              {% for activity in recent_activities %}
                <div class="timeline-block mb-3">
                  <span class="timeline-step bg-{{ activity.color }}">
                    <i class="fas fa-{{ activity.icon }} text-white"></i>
                  </span>
                  <div class="timeline-content">
                    <h6 class="text-dark text-sm font-weight-bold mb-0">{{ activity.message }}</h6>
                    <p class="text-secondary font-weight-bold text-xs mt-1 mb-0">{{ activity.time }}</p>
                  </div>
                </div>
              {% endfor %}
            {% else %}
              <p class="text-center">هیچ فعالیتی ثبت نشده است.</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
