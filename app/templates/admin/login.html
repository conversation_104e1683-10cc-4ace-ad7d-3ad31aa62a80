<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ورود مدیریت - Rominext</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/rtl.css') }}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    body {
      background-color: #f5f5f5;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .login-card {
      max-width: 400px;
      width: 100%;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .login-header {
      background-color: #343a40;
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .login-logo {
      margin-bottom: 15px;
    }
    
    .login-body {
      padding: 30px;
    }
    
    .btn-login {
      background-color: #343a40;
      border-color: #343a40;
    }
    
    .btn-login:hover {
      background-color: #23272b;
      border-color: #23272b;
    }
  </style>
</head>
<body>
  <div class="login-card">
    <div class="login-header">
      <div class="login-logo text-center">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Rominext" class="img-fluid" style="max-width: 150px;">
      </div>
      <h4 class="mb-0">ورود به پنل مدیریت</h4>
    </div>
    <div class="login-body">
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          {% for category, message in messages %}
            <div class="alert alert-{{ category }}">{{ message }}</div>
          {% endfor %}
        {% endif %}
      {% endwith %}
      
      <form method="POST" action="{{ url_for('admin.login') }}">
        <div class="mb-3">
          <label for="email" class="form-label">ایمیل</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
            <input type="email" class="form-control" id="email" name="email" required>
          </div>
        </div>
        <div class="mb-3">
          <label for="password" class="form-label">رمز عبور</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-lock"></i></span>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
        </div>
        <div class="mb-3 form-check">
          <input type="checkbox" class="form-check-input" id="remember" name="remember">
          <label class="form-check-label" for="remember">مرا به خاطر بسپار</label>
        </div>
        <div class="d-grid gap-2 mt-4">
          <button type="submit" class="btn btn-primary btn-login">ورود</button>
        </div>
      </form>
      <div class="text-center mt-3">
        <a href="{{ url_for('landing.index') }}" class="text-decoration-none">
          <i class="fas fa-arrow-right"></i> بازگشت به صفحه اصلی
        </a>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
