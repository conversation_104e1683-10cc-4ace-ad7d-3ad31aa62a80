{% extends "main_layout.html" %}

{% block title %}{{ t('blog.title') }} - Rominext{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold">{{ t('blog.title') }}</h1>
            <p class="lead">{{ t('blog.subtitle') }}</p>
        </div>
    </div>
    
    <div class="row">
        {% if blogs %}
            {% for blog in blogs %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        {% if blog.featured_image %}
                            <img src="{{ blog.featured_image }}" class="card-img-top" alt="{{ blog.title }}">
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ blog.title }}</h5>
                            <p class="card-text text-muted">{{ blog.published_at.strftime('%Y-%m-%d') }}</p>
                            <p class="card-text">{{ blog.content|truncate(150) }}</p>
                            <a href="{{ url_for('blog.view', slug=blog.slug) }}" class="btn btn-primary">Read More</a>
                        </div>
                        <div class="card-footer bg-transparent">
                            {% for tag in blog.tags %}
                                <span class="badge bg-secondary me-1">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12 text-center">
                <p>{{ t('blog.no_posts') }}</p>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if pages > 1 %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page-1) }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for p in range(1, pages + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('landing.blog', page=p) }}">{{ p }}</a>
                    </li>
                    {% endfor %}
                    
                    {% if page < pages %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page+1) }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

