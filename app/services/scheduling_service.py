from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import logging
import asyncio
from ..models.post import Post
from ..models.post_schedule import PostSchedule, RecurrenceType
from ..integrations.ai.base_ai_provider import BaseAIProvider
from ..integrations.social.base_social_connector import BaseSocialIntegration as BaseSocialConnector
from ..services.social_connector_manager import SocialConnectorManager
from ..services.ai_provider_manager import AIProviderManager

logger = logging.getLogger(__name__)

class SchedulingService:
    """Service for scheduling and publishing posts across multiple social platforms"""
    
    def __init__(self, ai_provider_manager: AIProviderManager, social_connector_manager: SocialConnectorManager):
        """
        Initialize SchedulingService with AI provider manager and social connector manager
        
        Args:
            ai_provider_manager: Manager for AI providers
            social_connector_manager: Manager for social media connectors
        """
        self.ai_provider_manager = ai_provider_manager
        self.social_connector_manager = social_connector_manager
        self.max_retries = 3
        self.retry_delay = 2  # seconds
    
    def schedule_post(self, user, content, scheduled_time, platform_name=None):
        """
        Schedule a post for publishing at a specific time
        
        Args:
            user: User creating the post
            content: Post content
            scheduled_time: When to publish the post
            platform_name: Name of the social platform (uses default if None)
        
        Returns:
            Scheduled post details
        """
        # Select AI provider based on user preferences
        ai_provider = self.ai_provider_manager.select_provider_for_user(user.id)
        
        # Select social connector based on platform name or use default
        if platform_name:
            social_connector = self.social_connector_manager.get_connector(platform_name)
            if not social_connector:
                raise ValueError(f"Platform '{platform_name}' not found")
        else:
            social_connector = self.social_connector_manager.get_default_connector()
            if not social_connector:
                raise ValueError("No default social platform configured")
        
        # Process content with AI if needed
        # processed_content = ai_provider.process_content(content)
        
        # Schedule the post
        result = social_connector.schedule_content(
            {"message": content},
            scheduled_time.isoformat()
        )
        
        # Save to database
        # ... code to save scheduled post ...
        
        return result
    
    async def publish_omni_post(self, post_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Publish content across all connected channels at once (Omni-post)
        
        Args:
            post_data: Post content and metadata
            
        Returns:
            Dictionary with results from each platform
        """
        results = {}
        tasks = []
        
        try:
            logger.info(f"Publishing omni-post across {len(self.social_connectors)} platforms")
            
            # Create post record
            post = Post(
                content=post_data.get("content", ""),
                content_type=post_data.get("content_type", "text"),
                media_url=post_data.get("media_url", ""),
                status="publishing",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ).save()
            
            # Create tasks for each connector
            for connector in self.social_connectors:
                platform_name = connector.__class__.__name__
                task = self._publish_with_retry(connector, post_data, platform_name)
                tasks.append(task)
            
            # Execute all tasks concurrently
            platform_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(platform_results):
                platform_name = self.social_connectors[i].__class__.__name__
                if isinstance(result, Exception):
                    logger.error(f"Failed to publish to {platform_name}: {str(result)}")
                    results[platform_name] = {"success": False, "error": str(result)}
                else:
                    results[platform_name] = {"success": True, "data": result}
            
            # Update post status
            all_successful = all(r.get("success", False) for r in results.values())
            post.status = "published" if all_successful else "partial_publish"
            post.published_at = datetime.utcnow()
            post.save()
            
            return {
                "post_id": str(post.id),
                "status": post.status,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error in omni-post publishing: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _publish_with_retry(self, connector: BaseSocialConnector, 
                                post_data: Dict[str, Any], platform_name: str) -> Dict[str, Any]:
        """Helper method to publish with retry logic"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Publishing to {platform_name}, attempt {attempt+1}/{self.max_retries}")
                result = await connector.publish_content(post_data)
                return result
            except Exception as e:
                logger.warning(f"Attempt {attempt+1} failed for {platform_name}: {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    raise
    
    async def suggest_golden_hour(self, platform: Optional[str] = None) -> Dict[str, Any]:
        """
        Suggest the best time to post for maximum engagement
        
        Args:
            platform: Optional specific platform to get suggestion for
            
        Returns:
            Dictionary with suggested times and confidence scores
        """
        try:
            logger.info(f"Generating golden hour suggestions for {platform or 'all platforms'}")
            
            # Prepare prompt for AI provider
            prompt = self._build_golden_hour_prompt(platform)
            
            # Get suggestion from AI provider
            suggestion_data = await self.ai_provider.generate_content(prompt, {
                "content_type": "golden_hour_suggestion",
                "platform": platform
            })
            
            # Process and format the suggestion
            processed_suggestion = self._process_golden_hour_suggestion(suggestion_data)
            
            return {
                "success": True,
                "suggestions": processed_suggestion
            }
            
        except Exception as e:
            logger.error(f"Error generating golden hour suggestion: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestions": self._get_fallback_golden_hour()
            }
    
    def _build_golden_hour_prompt(self, platform: Optional[str]) -> str:
        """Build prompt for golden hour suggestion"""
        if platform:
            return f"Based on engagement patterns, what is the best time to post on {platform} for maximum reach and engagement?"
        else:
            return "Based on engagement patterns across social media platforms, what are the best times to post for maximum reach and engagement?"
    
    def _process_golden_hour_suggestion(self, suggestion_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and format the golden hour suggestion from AI"""
        # This would normally parse the AI response into a structured format
        # For now, we'll return a simplified version
        return {
            "times": suggestion_data.get("times", []),
            "rationale": suggestion_data.get("rationale", ""),
            "confidence": suggestion_data.get("confidence", 0.7)
        }
    
    def _get_fallback_golden_hour(self) -> Dict[str, Any]:
        """Provide fallback golden hour suggestions if AI fails"""
        return {
            "times": [
                {"day": "weekday", "time": "12:00", "platform": "general"},
                {"day": "weekday", "time": "18:00", "platform": "general"},
                {"day": "weekend", "time": "11:00", "platform": "general"}
            ],
            "rationale": "These are general best practices based on industry standards.",
            "confidence": 0.5
        }
    
    async def process_scheduled_posts(self) -> Dict[str, Any]:
        """
        Process all posts scheduled for publishing
        
        Returns:
            Summary of processed posts
        """
        try:
            now = datetime.utcnow()
            pending_schedules = PostSchedule.objects(scheduled_at__lte=now)
            
            logger.info(f"Processing {pending_schedules.count()} scheduled posts")
            
            results = {
                "total": pending_schedules.count(),
                "successful": 0,
                "failed": 0,
                "details": []
            }
            
            for schedule in pending_schedules:
                post = schedule.post
                post_data = {
                    "content": post.content,
                    "content_type": post.content_type,
                    "media_url": post.media_url
                }
                
                try:
                    # Publish to all platforms
                    publish_result = await self.publish_omni_post(post_data)
                    
                    if publish_result.get("status") == "published":
                        results["successful"] += 1
                    else:
                        results["failed"] += 1
                    
                    results["details"].append({
                        "post_id": str(post.id),
                        "schedule_id": str(schedule.id),
                        "status": publish_result.get("status"),
                        "platforms": publish_result.get("results", {})
                    })
                    
                    # Handle recurrence
                    if schedule.recurrence != RecurrenceType.NONE:
                        self._reschedule_recurring_post(schedule)
                    else:
                        # Delete one-time schedule after processing
                        schedule.delete()
                        
                except Exception as e:
                    logger.error(f"Error processing scheduled post {schedule.id}: {str(e)}")
                    results["failed"] += 1
                    results["details"].append({
                        "post_id": str(post.id),
                        "schedule_id": str(schedule.id),
                        "status": "error",
                        "error": str(e)
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing scheduled posts: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _reschedule_recurring_post(self, schedule: PostSchedule) -> None:
        """Reschedule a recurring post based on its recurrence type"""
        next_time = None
        
        if schedule.recurrence == RecurrenceType.DAILY:
            next_time = schedule.scheduled_at + timedelta(days=1)
        elif schedule.recurrence == RecurrenceType.WEEKLY:
            next_time = schedule.scheduled_at + timedelta(weeks=1)
        elif schedule.recurrence == RecurrenceType.MONTHLY:
            # Simple approximation for monthly
            next_time = schedule.scheduled_at + timedelta(days=30)
        
        if next_time:
            schedule.scheduled_at = next_time
            schedule.updated_at = datetime.utcnow()
            schedule.save()
            logger.info(f"Rescheduled recurring post {schedule.id} for {next_time}")









