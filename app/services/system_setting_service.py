from typing import Dict, List, Optional, Any, Union
import logging
from datetime import datetime
from ..models.system_setting import SystemSetting

logger = logging.getLogger(__name__)

class SystemSettingService:
    """Service for managing system-wide settings"""
    
    @staticmethod
    def get_setting(key: str) -> Optional[SystemSetting]:
        """
        Get a system setting by key
        
        Args:
            key: The setting key to retrieve
            
        Returns:
            SystemSetting object if found, None otherwise
        """
        try:
            return SystemSetting.objects(key=key).first()
        except Exception as e:
            logger.error(f"Error retrieving system setting '{key}': {str(e)}")
            return None
    
    @staticmethod
    def get_setting_value(key: str, default: Any = None) -> Any:
        """
        Get a system setting value by key
        
        Args:
            key: The setting key to retrieve
            default: Default value to return if setting not found
            
        Returns:
            The setting value or default if not found
        """
        setting = SystemSettingService.get_setting(key)
        return setting.value if setting else default
    
    @staticmethod
    def set_setting(key: str, value: str, description: Optional[str] = None) -> Optional[SystemSetting]:
        """
        Set a system setting value
        
        Args:
            key: The setting key
            value: The setting value
            description: Optional description of the setting
            
        Returns:
            Updated or created SystemSetting object
        """
        try:
            setting = SystemSetting.objects(key=key).first()
            
            if setting:
                # Update existing setting
                setting.value = value
                if description:
                    setting.description = description
                setting.updated_at = datetime.utcnow()
                setting.save()
                return setting
            else:
                # Create new setting
                setting = SystemSetting(
                    key=key,
                    value=value,
                    description=description
                )
                setting.save()
                return setting
        except Exception as e:
            logger.error(f"Error setting system setting '{key}': {str(e)}")
            return None
    
    @staticmethod
    def delete_setting(key: str) -> bool:
        """
        Delete a system setting
        
        Args:
            key: The setting key to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            setting = SystemSetting.objects(key=key).first()
            if setting:
                setting.delete()
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting system setting '{key}': {str(e)}")
            return False
    
    @staticmethod
    def get_all_settings() -> List[SystemSetting]:
        """
        Get all system settings
        
        Returns:
            List of all SystemSetting objects
        """
        try:
            return list(SystemSetting.objects.all())
        except Exception as e:
            logger.error(f"Error retrieving all system settings: {str(e)}")
            return []
    
    @staticmethod
    def get_settings_by_prefix(prefix: str) -> List[SystemSetting]:
        """
        Get all system settings with keys starting with the given prefix
        
        Args:
            prefix: The key prefix to filter by
            
        Returns:
            List of matching SystemSetting objects
        """
        try:
            return list(SystemSetting.objects(key__startswith=prefix))
        except Exception as e:
            logger.error(f"Error retrieving system settings with prefix '{prefix}': {str(e)}")
            return []