import json
import os
from flask import current_app, g, request, session

class TranslationService:
    def __init__(self, app=None):
        self.translations = {}
        self.default_lang = 'fa'  # Changed from 'en' to 'fa'
        self.supported_langs = ['en', 'fa']  # Keep supporting both languages
        
        if app:
            self.init_app(app)    
    
    def init_app(self, app):
        # Load all translation files
        translations_dir = os.path.join(app.root_path, 'translations')
        for lang in self.supported_langs:
            file_path = os.path.join(translations_dir, f'{lang}.json')
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.translations[lang] = json.load(f)
            except FileNotFoundError:
                app.logger.error(f"Translation file not found: {file_path}")
                self.translations[lang] = {}
        
        # Register context processor to make translate function available in templates
        @app.context_processor
        def inject_translate():
            return {'t': self.translate}
        
        # Set language based on session or request
        @app.before_request
        def set_language():
            g.lang = session.get('lang', self.default_lang)
    
    def translate(self, key, default=None):
        """
        Translate a key using dot notation (e.g., 'landing.hero.title')
        """
        if not key:
            return ""
        
        lang = g.get('lang', self.default_lang)
        
        if lang not in self.translations:
            lang = self.default_lang
        
        # Navigate through nested dictionary using the key path
        parts = key.split('.')
        value = self.translations[lang]
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                # Fallback to English if key not found in non-English language
                if lang != self.default_lang:
                    # Use the default language value instead of recursive call
                    default_value = self._get_value_from_default_lang(key)
                    return default_value if default_value is not None else (default or key)
                # If we're already in default language, just return the default or key
                return default if default is not None else key
        
        return value

    def _get_value_from_default_lang(self, key):
        """
        Get a value from the default language without recursion
        """
        parts = key.split('.')
        value = self.translations.get(self.default_lang, {})
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
        
        return value
