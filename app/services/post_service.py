from typing import List, Dict, Any, Optional, Union, TypeVar, Generic
from datetime import datetime
import logging
from abc import ABC, abstractmethod
import asyncio
from ..models.post import Post
from sqlalchemy.orm import Session
from ..utils.logging_helpers import log_event
from ..models.log import LogLevel, LogCategory

# Type definitions
T = TypeVar('T')
logger = logging.getLogger(__name__)

class BaseAIProvider(ABC):
    @abstractmethod
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate content based on prompt"""
        pass
    
    @abstractmethod
    async def suggest_content(self, preferences: Dict[str, Any], **kwargs) -> List[Dict[str, Any]]:
        """Suggest content ideas based on preferences"""
        pass

class BaseSocialConnector(ABC):
    @abstractmethod
    async def publish_post(self, post_data: Dict[str, Any]) -> Dict[str, Any]:
        """Publish post to social platform"""
        pass
    
    @abstractmethod
    async def schedule_post(self, post_data: Dict[str, Any], schedule_time: datetime) -> Dict[str, Any]:
        """Schedule post for future publishing"""
        pass

class PostService:
    """Service for managing post creation, suggestions, and scheduling"""
    
    def __init__(self, ai_provider: BaseAIProvider, social_connector: BaseSocialConnector, db_session: Session):
        """
        Initialize PostService with AI provider and social connector
        
        Args:
            ai_provider: Provider for AI content generation
            social_connector: Connector for social media platforms
            db_session: Database session for ORM operations
        """
        self.ai_provider = ai_provider
        self.social_connector = social_connector
        self.db_session = db_session
        self.logger = logger
    
    async def generate_post(self, 
                     prompt: str, 
                     content_type: str = "text", 
                     metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate a post using AI provider
        
        Args:
            prompt: Content generation prompt
            content_type: Type of content (text, image, video)
            metadata: Additional metadata for generation
            
        Returns:
            Dictionary containing generated post data
        """
        try:
            metadata = metadata or {}
            self.logger.info(f"Generating {content_type} post with prompt: {prompt[:50]}...")
            
            generation_params = {
                "content_type": content_type,
                **metadata
            }
            
            result = await self.ai_provider.generate_content(prompt, **generation_params)
            
            # Create post record in database
            post = Post(
                content=result.get("content", ""),
                content_type=content_type,
                media_url=result.get("media_url", ""),
                status="draft",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db_session.add(post)
            self.db_session.commit()
            
            return {
                "post_id": post.id,
                "content": result.get("content", ""),
                "media_url": result.get("media_url", ""),
                "hashtags": result.get("hashtags", []),
                "content_type": content_type
            }
            
        except Exception as e:
            self.logger.error(f"Error generating post: {str(e)}")
            self.db_session.rollback()
            raise
    
    async def get_post_suggestions(self, 
                           preferences: Dict[str, Any], 
                           count: int = 3) -> List[Dict[str, Any]]:
        """
        Get post suggestions based on user preferences
        
        Args:
            preferences: User preferences for content
            count: Number of suggestions to generate
            
        Returns:
            List of post suggestions
        """
        try:
            self.logger.info(f"Generating {count} post suggestions based on preferences")
            
            suggestions = await self.ai_provider.suggest_content(
                preferences=preferences,
                count=count
            )
            
            return suggestions
        except Exception as e:
            self.logger.error(f"Error getting post suggestions: {str(e)}")
            return []
    
    async def create_serial_content(self, 
                            topic: str, 
                            parts: int, 
                            content_type: str = "text",
                            metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create serial/episodic content with multiple parts
        
        Args:
            topic: Main topic for the serial content
            parts: Number of parts to generate
            content_type: Type of content
            metadata: Additional metadata
            
        Returns:
            Dictionary with serial content data
        """
        try:
            metadata = metadata or {}
            self.logger.info(f"Creating serial content with {parts} parts on topic: {topic}")
            
            # Enhanced prompt for serial content
            prompt = f"Create a {parts}-part series about {topic}. Each part should be cohesive yet standalone."
            
            generation_params = {
                "content_type": content_type,
                "is_serial": True,
                "parts": parts,
                **metadata
            }
            
            result = await self.ai_provider.generate_content(prompt, **generation_params)
            
            # Process and store each part
            posts = []
            for i, part_content in enumerate(result.get("parts", [])):
                post = Post(
                    content=part_content.get("content", ""),
                    content_type=content_type,
                    media_url=part_content.get("media_url", ""),
                    status="draft",
                    serial_index=i+1,
                    serial_total=parts,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db_session.add(post)
                posts.append(post)
            
            self.db_session.commit()
            
            return {
                "serial_id": result.get("serial_id", ""),
                "topic": topic,
                "parts": [{"post_id": post.id, "content": post.content} for post in posts],
                "total_parts": parts
            }
            
        except Exception as e:
            self.logger.error(f"Error creating serial content: {str(e)}")
            self.db_session.rollback()
            raise
    
    async def create_campaign(self, 
                      name: str,
                      posts: List[Dict[str, Any]], 
                      schedule: List[datetime]) -> Dict[str, Any]:
        """
        Create a campaign with multiple scheduled posts
        
        Args:
            name: Campaign name
            posts: List of post data
            schedule: List of scheduled times for each post
            
        Returns:
            Campaign data with scheduled posts
        """
        if len(posts) != len(schedule):
            raise ValueError("Number of posts must match number of scheduled times")
        
        try:
            self.logger.info(f"Creating campaign '{name}' with {len(posts)} posts")
            
            campaign_posts = []
            
            for i, (post_data, schedule_time) in enumerate(zip(posts, schedule)):
                # Schedule the post
                schedule_result = await self.social_connector.schedule_post(
                    post_data=post_data,
                    schedule_time=schedule_time
                )
                
                # Create post record
                post = Post(
                    content=post_data.get("content", ""),
                    content_type=post_data.get("content_type", "text"),
                    media_url=post_data.get("media_url", ""),
                    status="scheduled",
                    scheduled_at=schedule_time,
                    campaign_name=name,
                    campaign_index=i+1,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db_session.add(post)
                campaign_posts.append({
                    "post_data": post_data,
                    "schedule_time": schedule_time,
                    "schedule_result": schedule_result
                })
            
            self.db_session.commit()
            
            return {
                "campaign_name": name,
                "posts": campaign_posts,
                "total_posts": len(posts)
            }
            
        except Exception as e:
            self.logger.error(f"Error creating campaign: {str(e)}")
            self.db_session.rollback()
            raise
    
    async def retry_failed_post(self, post_id: int, max_retries: int = 3) -> Dict[str, Any]:
        """
        Retry publishing a failed post
        
        Args:
            post_id: ID of the failed post
            max_retries: Maximum number of retry attempts
            
        Returns:
            Result of the retry operation
        """
        post = self.db_session.query(Post).filter(Post.id == post_id).first()
        
        if not post:
            raise ValueError(f"Post with ID {post_id} not found")
        
        if post.status not in ["failed", "error"]:
            raise ValueError(f"Post status '{post.status}' is not eligible for retry")
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"Retry attempt {attempt+1}/{max_retries} for post {post_id}")
                
                post_data = {
                    "content": post.content,
                    "content_type": post.content_type,
                    "media_url": post.media_url
                }
                
                if post.scheduled_at and post.scheduled_at > datetime.utcnow():
                    result = await self.social_connector.schedule_post(
                        post_data=post_data,
                        schedule_time=post.scheduled_at
                    )
                    post.status = "scheduled"
                else:
                    result = await self.social_connector.publish_post(post_data=post_data)
                    post.status = "published"
                    post.published_at = datetime.utcnow()
                
                post.updated_at = datetime.utcnow()
                self.db_session.commit()
                
                return {
                    "post_id": post_id,
                    "status": post.status,
                    "result": result
                }
                
            except Exception as e:
                self.logger.error(f"Retry attempt {attempt+1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    post.status = "failed"
                    post.updated_at = datetime.utcnow()
                    self.db_session.commit()
                    raise

    def get_user_posts(self, user_id, page=1, per_page=10, platform=None, date_from=None, date_to=None):
        """
        Get posts for a user with filtering and pagination
        
        Args:
            user_id: The user ID
            page: Page number (default: 1)
            per_page: Items per page (default: 10)
            platform: Filter by platform (optional)
            date_from: Filter by date from (format: YYYY-MM-DD, optional)
            date_to: Filter by date to (format: YYYY-MM-DD, optional)
        
        Returns:
            Dict containing posts, total count, and total pages
        """
        self.logger.info(f"Getting posts for user {user_id} with filters")
        
        # Build query filters
        filters = {"user_id": user_id}
        
        if platform:
            filters["platform"] = platform
        
        date_filters = {}
        if date_from:
            try:
                from_date = datetime.strptime(date_from, "%Y-%m-%d")
                date_filters["$gte"] = from_date
            except ValueError:
                raise ValueError("Invalid date_from format. Use YYYY-MM-DD")
        
        if date_to:
            try:
                to_date = datetime.strptime(date_to, "%Y-%m-%d")
                # Set to end of day
                to_date = to_date.replace(hour=23, minute=59, second=59)
                date_filters["$lte"] = to_date
            except ValueError:
                raise ValueError("Invalid date_to format. Use YYYY-MM-DD")
        
        if date_filters:
            filters["created_at"] = date_filters
        
        # Calculate pagination
        skip = (page - 1) * per_page
        
        # Query posts
        posts = self.db_session.query(Post).filter_by(**filters).order_by(
            Post.created_at.desc()
        ).offset(skip).limit(per_page).all()
        
        # Get total count for pagination
        total = self.db_session.query(Post).filter_by(**filters).count()
        total_pages = (total + per_page - 1) // per_page  # Ceiling division
        
        # Format posts for response
        formatted_posts = []
        for post in posts:
            formatted_posts.append({
                "id": str(post.id),
                "content": post.content,
                "content_type": post.content_type,
                "platform": post.platform,
                "status": post.status,
                "created_at": post.created_at.isoformat(),
                "scheduled_at": post.scheduled_at.isoformat() if post.scheduled_at else None,
                "published_at": post.published_at.isoformat() if post.published_at else None,
                "media_url": post.media_url,
                "campaign_name": post.campaign_name
            })
        
        return {
            "posts": formatted_posts,
            "total": total,
            "pages": total_pages
        }
