import logging
from datetime import datetime
from ..models import Blog, User
from slugify import slugify  # Make sure python-slugify is installed

class BlogService:
    def __init__(self, db_session=None):
        self.logger = logging.getLogger(__name__)
        self.db_session = db_session
    
    def create_blog(self, user_id, title, content, tags=None, featured_image=None, is_published=False):
        """Create a new blog post"""
        try:
            user = User.objects.get(id=user_id)
            
            # Generate slug from title
            base_slug = slugify(title)
            slug = base_slug
            
            # Check if slug exists and make it unique if needed
            count = 1
            while Blog.objects(slug=slug).count() > 0:
                slug = f"{base_slug}-{count}"
                count += 1
            
            blog = Blog(
                user=user,
                title=title,
                content=content,
                slug=slug,
                tags=tags or [],
                featured_image=featured_image,
                is_published=is_published,
                published_at=datetime.utcnow() if is_published else None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            blog.save()
            return blog
        except Exception as e:
            self.logger.error(f"Error creating blog: {str(e)}")
            raise
    
    def get_blog_by_slug(self, slug):
        """Get blog by slug"""
        return Blog.objects.get(slug=slug)
    
    def get_blogs(self, user_id=None, published_only=False, limit=10, skip=0):
        """Get blogs with optional filtering"""
        query = {}
        
        if user_id:
            query['user'] = user_id
            
        if published_only:
            query['is_published'] = True
            
        return Blog.objects(**query).order_by('-created_at').skip(skip).limit(limit)
    
    def update_blog(self, blog_id, **kwargs):
        """Update blog post"""
        try:
            blog = Blog.objects.get(id=blog_id)
            
            # Update fields
            for key, value in kwargs.items():
                if hasattr(blog, key):
                    setattr(blog, key, value)
            
            # Update slug if title changed
            if 'title' in kwargs:
                base_slug = slugify(kwargs['title'])
                slug = base_slug
                
                # Check if slug exists and make it unique if needed
                count = 1
                while Blog.objects(slug=slug, id__ne=blog_id).count() > 0:
                    slug = f"{base_slug}-{count}"
                    count += 1
                
                blog.slug = slug
            
            # Update published_at if publishing status changed
            if 'is_published' in kwargs and kwargs['is_published'] and not blog.published_at:
                blog.published_at = datetime.utcnow()
            
            blog.updated_at = datetime.utcnow()
            blog.save()
            return blog
        except Exception as e:
            self.logger.error(f"Error updating blog: {str(e)}")
            raise
    
    def delete_blog(self, blog_id):
        """Delete a blog post"""
        try:
            blog = Blog.objects.get(id=blog_id)
            blog.delete()
            return True
        except Exception as e:
            self.logger.error(f"Error deleting blog: {str(e)}")
            raise


