import yaml
import os
from typing import Dict, Any, Optional, List, Callable, Type, Union
import logging
import random
from ..integrations.ai.base_ai_provider import BaseAIProvider

logger = logging.getLogger(__name__)

class AIProviderManager:
    """
    Manages multiple AI providers and handles provider selection,
    failover, and load balancing.
    """
    
    def __init__(self, providers: Dict[str, BaseAIProvider] = None, config_path: str = None):
        """
        Initialize the manager with available providers and optional config file.
        
        Args:
            providers: Dictionary mapping provider names to provider instances
            config_path: Path to configuration file for provider preferences
        """
        self.providers = providers or {}
        self.default_provider = next(iter(self.providers.values()), None) if self.providers else None
        self.provider_config = {}
        
        # Load configuration if provided
        if config_path and os.path.exists(config_path):
            self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> None:
        """
        Load provider configuration from YAML file.
        
        Args:
            config_path: Path to configuration file
        """
        try:
            with open(config_path, 'r') as file:
                self.provider_config = yaml.safe_load(file) or {}
                logger.info(f"Loaded AI provider configuration from {config_path}")
        except Exception as e:
            logger.error(f"Failed to load AI provider configuration: {str(e)}")
            self.provider_config = {}
    
    def add_provider(self, name: str, provider: BaseAIProvider) -> None:
        """
        Add a provider to the manager.
        
        Args:
            name: Provider name
            provider: Provider instance
        """
        self.providers[name] = provider
        if self.default_provider is None:
            self.default_provider = provider
    
    def get_provider(self, provider_name: str) -> Optional[BaseAIProvider]:
        """
        Get a provider by name.
        
        Args:
            provider_name: Name of the provider to retrieve
            
        Returns:
            Provider instance or None if not found
        """
        return self.providers.get(provider_name)
    
    def set_default_provider(self, provider_name: str) -> bool:
        """
        Set the default provider.
        
        Args:
            provider_name: Name of the provider to set as default
            
        Returns:
            True if successful, False if provider not found
        """
        provider = self.get_provider(provider_name)
        if provider:
            self.default_provider = provider
            return True
        return False
    
    def select_provider(self, context: Dict[str, Any] = None) -> Optional[BaseAIProvider]:
        """
        Select a provider based on context and configuration.
        
        Args:
            context: Dictionary with selection criteria
                - provider_name: Explicit provider name
                - content_type: Type of content (e.g., 'text', 'image')
                - task_type: Type of task (e.g., 'text_generation', 'image_generation')
                - model: Specific model name
                - priority_list: List of provider names in priority order
                
        Returns:
            Selected provider instance or default provider
        """
        context = context or {}
        
        # Direct provider selection
        if provider_name := context.get('provider_name'):
            if provider := self.get_provider(provider_name):
                return provider
        
        # Selection by content type from configuration
        if content_type := context.get('content_type'):
            # Check if we have a configured provider for this content type
            if content_config := self.provider_config.get('content', {}):
                if provider_name := content_config.get(content_type):
                    if provider := self.get_provider(provider_name):
                        return provider
        
        # Selection by task type from configuration
        if task_type := context.get('task_type'):
            # Check if we have a configured provider for this task type
            if task_config := self.provider_config.get('tasks', {}):
                if provider_name := task_config.get(task_type):
                    if provider := self.get_provider(provider_name):
                        return provider
        
        # Selection by priority list
        if priority_list := context.get('priority_list'):
            for name in priority_list:
                if provider := self.get_provider(name):
                    return provider
        
        # Selection by task type
        if task_type := context.get('task_type'):
            suitable_providers = [
                p for p in self.providers.values() 
                if hasattr(p, 'supported_tasks') and task_type in p.supported_tasks
            ]
            if suitable_providers:
                return random.choice(suitable_providers)  # Simple load balancing
        
        # Fallback to default
        return self.default_provider
    
    def execute_with_failover(self, 
                             method_name: str, 
                             args: tuple = None, 
                             kwargs: Dict[str, Any] = None, 
                             context: Dict[str, Any] = None,
                             max_attempts: int = 3) -> Any:
        """
        Execute a method with automatic failover if it fails.
        
        Args:
            method_name: Name of the method to call on the provider
            args: Positional arguments for the method
            kwargs: Keyword arguments for the method
            context: Context for provider selection
            max_attempts: Maximum number of failover attempts
            
        Returns:
            Result from the method call
            
        Raises:
            Exception: If all providers fail
        """
        args = args or ()
        kwargs = kwargs or {}
        context = context or {}
        
        # Get providers to try in order
        providers_to_try = []
        
        # First try explicitly requested provider
        if provider_name := context.get('provider_name'):
            if provider := self.get_provider(provider_name):
                providers_to_try.append(provider)
        
        # Then try providers from priority list
        if priority_list := context.get('priority_list'):
            for name in priority_list:
                if provider := self.get_provider(name):
                    if provider not in providers_to_try:
                        providers_to_try.append(provider)
        
        # Add all remaining providers
        for provider in self.providers.values():
            if provider not in providers_to_try:
                providers_to_try.append(provider)
        
        # Limit to max_attempts
        providers_to_try = providers_to_try[:max_attempts]
        
        last_error = None
        for provider in providers_to_try:
            try:
                if hasattr(provider, method_name):
                    method = getattr(provider, method_name)
                    return method(*args, **kwargs)
                else:
                    logger.warning(f"Provider {provider.__class__.__name__} does not have method {method_name}")
                    continue
            except Exception as e:
                logger.warning(f"Provider {provider.__class__.__name__} failed: {str(e)}")
                last_error = e
                continue
        
        # If we get here, all providers failed
        error_msg = f"All providers failed to execute {method_name}"
        if last_error:
            error_msg += f": {str(last_error)}"
        raise Exception(error_msg)


class UserAIProviderManager(AIProviderManager):
    """
    Extends AIProviderManager to handle user-specific provider preferences.
    """
    
    def __init__(self, providers: Dict[str, BaseAIProvider] = None, user_preferences: Dict[str, Dict] = None):
        """
        Initialize with providers and user preferences.
        
        Args:
            providers: Dictionary mapping provider names to provider instances
            user_preferences: Dictionary mapping user IDs to their preferences
        """
        super().__init__(providers)
        self.user_preferences = user_preferences or {}
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get preferences for a specific user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary of user preferences or empty dict if not found
        """
        return self.user_preferences.get(user_id, {})
    
    def set_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> None:
        """
        Set preferences for a specific user.
        
        Args:
            user_id: User identifier
            preferences: Dictionary of user preferences
        """
        self.user_preferences[user_id] = preferences
    
    def select_provider_for_user(self, user_id: str, context: Dict[str, Any] = None) -> Optional[BaseAIProvider]:
        """
        Select a provider based on user preferences and context.
        
        Args:
            user_id: User identifier
            context: Additional context for selection
            
        Returns:
            Selected provider instance
        """
        context = context or {}
        user_prefs = self.get_user_preferences(user_id)
        
        # Merge user preferences into context
        merged_context = {**context}
        
        # Add user's preferred provider to priority list
        if preferred_provider := user_prefs.get('preferred_provider'):
            if 'priority_list' in merged_context:
                if preferred_provider not in merged_context['priority_list']:
                    merged_context['priority_list'].insert(0, preferred_provider)
            else:
                merged_context['priority_list'] = [preferred_provider]
        
        # Add user's preferred model if applicable
        if preferred_model := user_prefs.get('preferred_model'):
            merged_context['model'] = preferred_model
        
        return self.select_provider(merged_context)
    
    def execute_for_user(self, 
                        user_id: str,
                        method_name: str, 
                        args: tuple = None, 
                        kwargs: Dict[str, Any] = None, 
                        context: Dict[str, Any] = None,
                        max_attempts: int = 3) -> Any:
        """
        Execute a method for a specific user with failover.
        
        Args:
            user_id: User identifier
            method_name: Name of the method to call on the provider
            args: Positional arguments for the method
            kwargs: Keyword arguments for the method
            context: Additional context for selection
            max_attempts: Maximum number of failover attempts
            
        Returns:
            Result from the method call
        """
        context = context or {}
        user_prefs = self.get_user_preferences(user_id)
        
        # Merge user preferences into context
        merged_context = {**context}
        
        # Add user's preferred provider to priority list
        if preferred_provider := user_prefs.get('preferred_provider'):
            if 'priority_list' in merged_context:
                if preferred_provider not in merged_context['priority_list']:
                    merged_context['priority_list'].insert(0, preferred_provider)
            else:
                merged_context['priority_list'] = [preferred_provider]
        
        return self.execute_with_failover(method_name, args, kwargs, merged_context, max_attempts)

