from werkzeug.security import generate_password_hash, check_password_hash
from ..models import User
from typing import Optional

class VaultService:
    @staticmethod
    def create_user(email: str, password: str, name: str = None) -> User:
        """Create a new user with the given email and password"""
        user = User(email=email)
        user.set_password(password)
        if name:
            user.name = name
        return user.save()

    @staticmethod
    def verify_user(email: str, password: str) -> Optional[User]:
        user = User.objects(email=email).first()
        if user and user.check_password(password):
            return user
        return None

    @staticmethod
    def get_user_by_id(user_id: str) -> Optional[User]:
        return User.objects(id=user_id).first()
