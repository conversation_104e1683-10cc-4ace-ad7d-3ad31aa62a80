from typing import List, Optional
from datetime import datetime
from ..models.subscription import Subscription, SubscriptionStatus
from ..models.user import User
import logging

logger = logging.getLogger(__name__)

class SubscriptionService:
    """Service for managing user subscriptions"""
    
    @staticmethod
    def create_subscription(
        user_id: str, 
        plan_name: str, 
        start_date: datetime, 
        end_date: datetime, 
        posts_limit: int = 100, 
        accounts_limit: int = 5,  # Changed from channels_limit
        agents_limit: int = 3
    ) -> Subscription:
        """
        Create a new subscription for a user
        
        Args:
            user_id: The ID of the user
            plan_name: The name of the subscription plan
            start_date: When the subscription starts
            end_date: When the subscription ends
            posts_limit: Maximum posts per month
            accounts_limit: Maximum accounts allowed  # Changed from channels_limit
            agents_limit: Maximum agents allowed
            
        Returns:
            The created Subscription object
        """
        try:
            user = User.objects.get(id=user_id)
            
            # Check if user already has an active subscription
            existing_sub = Subscription.objects(
                user=user, 
                status=SubscriptionStatus.ACTIVE
            ).first()
            
            if existing_sub:
                # Cancel the existing subscription
                existing_sub.status = SubscriptionStatus.CANCELED
                existing_sub.updated_at = datetime.utcnow()
                existing_sub.save()
            
            # Create new subscription
            subscription = Subscription(
                user=user,
                plan_name=plan_name,
                status=SubscriptionStatus.ACTIVE,
                start_date=start_date,
                end_date=end_date,
                posts_limit_per_month=posts_limit,
                accounts_limit=accounts_limit,  # Changed from channels_limit
                agents_limit=agents_limit
            )
            
            return subscription.save()
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise
    
    @staticmethod
    def get_subscription(user_id: str) -> Optional[Subscription]:
        """
        Get the active subscription for a user
        
        Args:
            user_id: The ID of the user
            
        Returns:
            The active Subscription object or None
        """
        try:
            user = User.objects.get(id=user_id)
            return Subscription.objects(
                user=user, 
                status=SubscriptionStatus.ACTIVE
            ).first()
        except Exception as e:
            logger.error(f"Error fetching subscription: {str(e)}")
            return None
    
    @staticmethod
    def update_subscription(
        subscription_id: str,
        plan_name: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        posts_limit: Optional[int] = None,
        accounts_limit: Optional[int] = None,  # Changed from channels_limit
        agents_limit: Optional[int] = None
    ) -> Optional[Subscription]:
        """
        Update an existing subscription
        
        Args:
            subscription_id: The ID of the subscription to update
            plan_name: New plan name (optional)
            start_date: New start date (optional)
            end_date: New end date (optional)
            posts_limit: New posts limit (optional)
            accounts_limit: New accounts limit (optional)  # Changed from channels_limit
            agents_limit: New agents limit (optional)
            
        Returns:
            The updated Subscription object or None
        """
        try:
            subscription = Subscription.objects.get(id=subscription_id)
            
            # Update fields if provided
            if plan_name:
                subscription.plan_name = plan_name
            if start_date:
                subscription.start_date = start_date
            if end_date:
                subscription.end_date = end_date
            if posts_limit is not None:
                subscription.posts_limit_per_month = posts_limit
            if accounts_limit is not None:  # Changed from channels_limit
                subscription.accounts_limit = accounts_limit  # Changed from channels_limit
            if agents_limit is not None:
                subscription.agents_limit = agents_limit
                
            subscription.updated_at = datetime.utcnow()
            return subscription.save()
        except Exception as e:
            logger.error(f"Error updating subscription: {str(e)}")
            return None
    
    @staticmethod
    def cancel_subscription(subscription_id: str) -> bool:
        """
        Cancel a subscription
        
        Args:
            subscription_id: The ID of the subscription to cancel
            
        Returns:
            True if successful, False otherwise
        """
        try:
            subscription = Subscription.objects.get(id=subscription_id)
            subscription.status = SubscriptionStatus.CANCELED
            subscription.updated_at = datetime.utcnow()
            subscription.save()
            return True
        except Exception as e:
            logger.error(f"Error canceling subscription: {str(e)}")
            return False
    
    @staticmethod
    def get_all_subscriptions() -> List[Subscription]:
        """
        Get all subscriptions (admin function)
        
        Returns:
            List of all Subscription objects
        """
        try:
            return list(Subscription.objects.all())
        except Exception as e:
            logger.error(f"Error fetching all subscriptions: {str(e)}")
            return []


