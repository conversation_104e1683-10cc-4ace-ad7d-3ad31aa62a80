"""
Content Generation Agent - A multi-agent system for automated content creation
using the CrewAI framework.
"""
import logging
import asyncio
import base64
import os
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from app.services.ai_provider_manager import AIProviderManager
from app.utils.logging_helpers import log_event
from app.models.log import LogLevel, LogCategory
from app.models.post import Post

# Configure logging
logger = logging.getLogger(__name__)

# =============== TOOLS ===============

class ContentFormatTool(BaseTool):
    """Tool for formatting content based on platform requirements"""
    
    name: str = "Content Format Tool"
    description: str = "Formats content based on platform-specific requirements"
    
    def _run(self, platform: str, content: str) -> str:
        """Format content based on platform guidelines"""
        logger.info(f"Formatting content for {platform}")
        
        platform_formats = {
            "twitter": {"max_length": 280, "style": "concise"},
            "instagram": {"max_length": 2200, "style": "visual-focused"},
            "linkedin": {"max_length": 3000, "style": "professional"},
            "facebook": {"max_length": 5000, "style": "conversational"}
        }
        
        format_info = platform_formats.get(platform.lower(), {"max_length": 1000, "style": "standard"})
        
        # Truncate if needed
        if len(content) > format_info["max_length"]:
            content = content[:format_info["max_length"]-3] + "..."
            
        log_event(
            level=LogLevel.INFO,
            category=LogCategory.CONTENT,
            message=f"Content formatted for {platform}",
            details=f"Applied {format_info['style']} style with max length {format_info['max_length']}"
        )
        
        return content

class ImageGenerationTool(BaseTool):
    """Tool for generating images using AI providers"""
    
    name: str = "Image Generator"
    description: str = "Generates images based on text descriptions"
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, prompt: str, style: str = "digital art", size: str = "1024x1024") -> str:
        """Generate an image using AI provider"""
        logger.info(f"Generating image with prompt: {prompt[:50]}...")
        
        try:
            # Combine prompt with style
            full_prompt = f"{prompt}, {style} style"
            
            # Use provider manager to select appropriate provider
            context = {"task_type": "image_generation"}
            
            # Execute with failover
            response = self.ai_provider_manager.execute_with_failover(
                "make_request",
                args=("images/generations",),
                kwargs={
                    "payload": {
                        "prompt": full_prompt,
                        "n": 1,
                        "size": size,
                        "response_format": "url"
                    }
                },
                context=context
            )
            
            image_url = response.get("data", [{}])[0].get("url", "")
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.CONTENT,
                message="Image generated successfully",
                details=f"Prompt: {prompt[:100]}..."
            )
            
            return image_url
        except Exception as e:
            logger.error(f"Error generating image: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message="Image generation failed",
                details=str(e)
            )
            return ""

class TrendAnalysisTool(BaseTool):
    """Tool for analyzing trending hashtags and keywords"""
    
    name: str = "Trend Analyzer"
    description: str = "Analyzes trending hashtags and keywords for a given platform"
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, platform: str, topic: str) -> Dict[str, List[str]]:
        """Get trending hashtags and keywords for a topic on a platform"""
        logger.info(f"Analyzing trends for {topic} on {platform}")
        
        try:
            # Use AI to generate relevant hashtags and keywords
            prompt = f"""
            Generate trending hashtags and SEO keywords for a {platform} post about {topic}.
            Return the result as a JSON object with two arrays:
            1. "hashtags": 5-10 relevant hashtags (including the # symbol)
            2. "keywords": 3-5 SEO keywords (without # symbol)
            
            Only return the JSON object, nothing else.
            """
            
            messages = [
                {"role": "system", "content": "You are a social media trend expert."},
                {"role": "user", "content": prompt}
            ]
            
            # Use provider manager to select appropriate provider
            context = {"task_type": "text_generation"}
            
            # Execute with failover
            response = self.ai_provider_manager.execute_with_failover(
                "chat_with_gpt",
                args=(messages,),
                kwargs={"model": "gpt-3.5-turbo", "max_tokens": 150},
                context=context
            )
            
            result_text = response.get("text", "")
            
            # Extract JSON from response
            import json
            import re
            
            # Try to find JSON in the response
            json_match = re.search(r'(\{.*\})', result_text, re.DOTALL)
            if json_match:
                result_json = json.loads(json_match.group(1))
            else:
                # Fallback to default values
                result_json = {
                    "hashtags": [f"#{topic}", "#trending", "#content", "#social", "#viral"],
                    "keywords": [topic, "trending", "content", "social media"]
                }
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.CONTENT,
                message=f"Trend analysis completed for {topic} on {platform}",
                details=f"Found {len(result_json.get('hashtags', []))} hashtags and {len(result_json.get('keywords', []))} keywords"
            )
            
            return result_json
        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message="Trend analysis failed",
                details=str(e)
            )
            return {
                "hashtags": [f"#{topic}", "#content", "#social"],
                "keywords": [topic, "content", "social media"]
            }

class TranslationTool(BaseTool):
    """Tool for translating content to different languages"""
    
    name: str = "Content Translator"
    description: str = "Translates content to target languages while preserving tone and context"
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, text: str, target_language: str) -> str:
        """Translate text to target language"""
        logger.info(f"Translating content to {target_language}")
        
        try:
            # Use AI provider for translation
            messages = [
                {"role": "system", "content": f"You are a professional translator. Translate the following text to {target_language} while preserving the tone, style, and context."},
                {"role": "user", "content": text}
            ]
            
            # Use provider manager to select appropriate provider
            context = {"task_type": "text_generation"}
            
            # Execute with failover
            response = self.ai_provider_manager.execute_with_failover(
                "chat_with_gpt",
                args=(messages,),
                kwargs={"model": "gpt-3.5-turbo", "max_tokens": 500},
                context=context
            )
            
            translated_text = response.get("text", "")
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.CONTENT,
                message=f"Content translated to {target_language}",
                details=f"Original length: {len(text)}, Translated length: {len(translated_text)}"
            )
            
            return translated_text
        except Exception as e:
            logger.error(f"Translation error: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message="Translation failed",
                details=str(e)
            )
            return text  # Return original text if translation fails

# =============== CONTENT GENERATION AGENT ===============

class ContentGenerationAgent:
    """
    Main content generation agent that coordinates multiple specialized agents
    to create complete social media content.
    """
    
    def __init__(self, ai_provider_manager: AIProviderManager, db_session=None):
        """
        Initialize the content generation agent with AI provider manager
        
        Args:
            ai_provider_manager: Manager for AI providers with failover capability
            db_session: Database session for storing generated content
        """
        # Set AI provider manager
        self.ai_provider_manager = ai_provider_manager
        self.db_session = db_session
        self.logger = logger
        
        # Initialize tools
        self.format_tool = ContentFormatTool()
        self.image_tool = ImageGenerationTool(ai_provider_manager)
        self.trend_tool = TrendAnalysisTool(ai_provider_manager)
        self.translation_tool = TranslationTool(ai_provider_manager)
        
        # Initialize agents
        self._init_agents()
        
        # Create the crew
        self._init_crew()
    
    def _init_agents(self):
        """Initialize all specialized agents"""
        # Copywriter Agent
        self.copywriter_agent = Agent(
            role="Expert Social Media Copywriter",
            goal="Create engaging, platform-optimized content that drives engagement",
            backstory="""You are an expert copywriter with years of experience creating 
            viral content across all major social platforms. You understand the nuances 
            of each platform and how to craft messages that resonate with their specific audiences.""",
            verbose=True,
            allow_delegation=True,
            tools=[self.format_tool]
        )
        
        # Image Creator Agent
        self.image_creator_agent = Agent(
            role="Visual Content Creator",
            goal="Generate compelling images that enhance social media content",
            backstory="""You are a talented digital artist with an eye for creating 
            visuals that capture attention and complement written content. You understand 
            design principles and how to create images that perform well on social media.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.image_tool]
        )
        
        # Hashtag/SEO Agent
        self.hashtag_seo_agent = Agent(
            role="Hashtag and SEO Specialist",
            goal="Optimize content discoverability with relevant hashtags and keywords",
            backstory="""You are a social media optimization expert who understands 
            how to make content discoverable. You stay on top of trending hashtags 
            and know how to select keywords that will help content reach its target audience.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.trend_tool]
        )
        
        # Translator Agent
        self.translator_agent = Agent(
            role="Content Translator",
            goal="Translate content while preserving tone, context, and cultural nuances",
            backstory="""You are a skilled linguist and translator with expertise in 
            multiple languages. You understand how to adapt content for different cultures 
            while maintaining the original message and tone.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.translation_tool]
        )
    
    def _init_crew(self):
        """Initialize the CrewAI crew with all agents"""
        self.crew = Crew(
            agents=[
                self.copywriter_agent,
                self.image_creator_agent,
                self.hashtag_seo_agent,
                self.translator_agent
            ],
            tasks=[],  # Tasks will be added dynamically
            verbose=True,
            process=Process.sequential  # Tasks will run in sequence
        )
    
    def _create_copywriter_task(self, prompt: str, platform: str, tone: str, include_cta: bool) -> Task:
        """Create a task for the copywriter agent"""
        return Task(
            description=f"""
            Write {platform} content about {prompt} with a {tone} tone.
            {' Include a call-to-action.' if include_cta else ''}
            The content should be optimized for the {platform} platform.
            """,
            agent=self.copywriter_agent,
            expected_output="Platform-optimized content with appropriate length and style"
        )
    
    def _create_image_task(self, prompt: str, style: str) -> Task:
        """Create a task for the image creator agent"""
        return Task(
            description=f"""
            Generate an image based on the following description: {prompt}
            The image should be in the style of {style}.
            Create a visual that will complement social media content and drive engagement.
            """,
            agent=self.image_creator_agent,
            expected_output="URL to a generated image that matches the description and style"
        )
    
    def _create_hashtag_task(self, content: str, platform: str, topic: str) -> Task:
        """Create a task for the hashtag/SEO agent"""
        return Task(
            description=f"""
            Analyze the following content and suggest relevant hashtags and SEO keywords:
            
            Content: {content}
            Platform: {platform}
            Topic: {topic}
            
            Provide 5-10 relevant hashtags and 3-5 SEO keywords that will maximize discoverability.
            """,
            agent=self.hashtag_seo_agent,
            expected_output="JSON with hashtags and keywords lists"
        )
    
    def _create_translation_task(self, content: str, target_language: str) -> Task:
        """Create a task for the translator agent"""
        return Task(
            description=f"""
            Translate the following content to {target_language}:
            
            {content}
            
            Preserve the tone, style, and cultural context in your translation.
            """,
            agent=self.translator_agent,
            expected_output=f"Content translated to {target_language}"
        )
    
    async def generate_content(self, 
                      prompt: str, 
                      platform: str = "general", 
                      content_type: str = "text", 
                      metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate content using the multi-agent system
        
        Args:
            prompt: Content generation prompt/topic
            platform: Target social media platform
            content_type: Type of content (text, image, mixed)
            metadata: Additional metadata for generation
            
        Returns:
            Dictionary containing generated content data
        """
        try:
            metadata = metadata or {}
            self.logger.info(f"Generating {content_type} content for {platform} with prompt: {prompt[:50]}...")
            
            # Extract parameters from metadata
            tone = metadata.get("tone", "conversational")
            include_cta = metadata.get("include_cta", True)
            visual_style = metadata.get("visual_style", "digital art")
            
            # Create copywriter task
            copywriter_task = self._create_copywriter_task(prompt, platform, tone, include_cta)
            
            # Run copywriter task
            self.crew.tasks = [copywriter_task]
            content_results = self.crew.kickoff()
            content = content_results[0] if content_results else ""
            
            # Generate image if requested
            image_url = ""
            if content_type in ["image", "mixed"]:
                image_task = self._create_image_task(prompt, visual_style)
                self.crew.tasks = [image_task]
                image_results = self.crew.kickoff()
                image_url = image_results[0] if image_results else ""
            
            # Generate hashtags and keywords
            hashtag_task = self._create_hashtag_task(content, platform, prompt)
            self.crew.tasks = [hashtag_task]
            hashtag_results = self.crew.kickoff()
            
            # Parse hashtag results
            import json
            import re
            
            hashtag_text = hashtag_results[0] if hashtag_results else "{}"
            json_match = re.search(r'(\{.*\})', hashtag_text, re.DOTALL)
            if json_match:
                optimization = json.loads(json_match.group(1))
            else:
                optimization = {
                    "hashtags": [f"#{prompt.split()[0]}", "#content", "#social"],
                    "keywords": [prompt.split()[0], "content", "social media"]
                }
            
            # Create post record in database if session is available
            post_id = None
            if self.db_session:
                post = Post(
                    content=content,
                    content_type=content_type,
                    media_url=image_url,
                    platform=platform,
                    status="draft",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db_session.add(post)
                self.db_session.commit()
                post_id = post.id
            
            return {
                "post_id": post_id,
                "content": content,
                "media_url": image_url,
                "hashtags": optimization.get("hashtags", []),
                "keywords": optimization.get("keywords", []),
                "platform": platform,
                "content_type": content_type
            }
            
        except Exception as e:
            self.logger.error(f"Error generating content: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message="Content generation failed",
                details=str(e)
            )
            return {
                "error": str(e),
                "content": "",
                "media_url": "",
                "hashtags": [],
                "keywords": []
            }
    
    async def translate_content(self, content_id: str, target_languages: List[str]) -> Dict[str, str]:
        """
        Translate content to multiple languages
        
        Args:
            content_id: ID of the content to translate
            target_languages: List of target languages
            
        Returns:
            Dictionary mapping language codes to translated content
        """
        try:
            self.logger.info(f"Translating content {content_id} to {len(target_languages)} languages")
            
            # Retrieve content from database if session is available
            content = ""
            if self.db_session:
                post = self.db_session.query(Post).filter(Post.id == content_id).first()
                if post:
                    content = post.content
            
            if not content:
                return {"error": "Content not found"}
            
            translations = {}
            
            # Translate to each target language
            for language in target_languages:
                translation_task = self._create_translation_task(content, language)
                self.crew.tasks = [translation_task]
                translation_results = self.crew.kickoff()
                translations[language] = translation_results[0] if translation_results else ""
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.CONTENT,
                message=f"Content {content_id} translated to {len(translations)} languages",
                details=f"Target languages: {', '.join(target_languages)}"
            )
            
            return translations
            
        except Exception as e:
            self.logger.error(f"Error translating content: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message="Content translation failed",
                details=str(e)
            )
            return {"error": str(e)}


