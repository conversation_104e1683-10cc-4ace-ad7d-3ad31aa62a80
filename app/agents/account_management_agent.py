"""
Account Management Agent - A multi-agent system for managing social media accounts
using the CrewAI framework.
"""
import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from app.services.ai_provider_manager import AIProviderManager
from app.utils.logging_helpers import log_event
from app.models.log import LogLevel, LogCategory
from app.models.account import Account, Platform
from app.integrations.social.base_social_connector import BaseSocialIntegration
from app.services.social_connector_manager import SocialConnectorManager

# Configure logging
logger = logging.getLogger(__name__)

# =============== TOOLS ===============

class EngagementAnalyticsTool(BaseTool):
    """Tool for analyzing engagement patterns and optimal posting times"""
    
    name: str = "Engagement Analytics"
    description: str = "Analyzes historical engagement data to determine optimal posting times"
    
    def _run(self, platform: str, content_type: str = "general", days_back: int = 30) -> Dict[str, Any]:
        """Analyze engagement patterns and return optimal posting times"""
        logger.info(f"Analyzing engagement for {platform}, content type: {content_type}")
        
        # Mock implementation - would connect to analytics database in production
        engagement_patterns = {
            "twitter": {
                "general": {"weekday": ["08:00", "12:30", "17:00"], "weekend": ["10:00", "15:00"]},
                "promotional": {"weekday": ["12:00", "18:30"], "weekend": ["11:00", "16:00"]},
            },
            "instagram": {
                "general": {"weekday": ["11:00", "15:00", "20:00"], "weekend": ["12:00", "18:00"]},
                "promotional": {"weekday": ["12:00", "19:30"], "weekend": ["13:00", "17:00"]},
            },
            "facebook": {
                "general": {"weekday": ["09:00", "13:00", "19:00"], "weekend": ["11:00", "16:00"]},
                "promotional": {"weekday": ["11:00", "15:30"], "weekend": ["12:00", "17:00"]},
            },
            "linkedin": {
                "general": {"weekday": ["08:00", "12:00", "17:00"], "weekend": ["10:00", "14:00"]},
                "promotional": {"weekday": ["09:00", "13:30"], "weekend": ["11:00", "15:00"]},
            },
            "telegram": {
                "general": {"weekday": ["09:00", "14:00", "18:00"], "weekend": ["11:00", "16:00"]},
                "promotional": {"weekday": ["10:00", "15:30"], "weekend": ["12:00", "17:00"]},
            }
        }
        
        platform_data = engagement_patterns.get(platform.lower(), engagement_patterns["twitter"])
        content_data = platform_data.get(content_type, platform_data["general"])
        
        # Determine if today is a weekday or weekend
        today = datetime.now()
        is_weekend = today.weekday() >= 5  # 5 = Saturday, 6 = Sunday
        
        time_slots = content_data["weekend"] if is_weekend else content_data["weekday"]
        
        log_event(
            level=LogLevel.INFO,
            category=LogCategory.ANALYTICS,
            message=f"Engagement analysis completed for {platform}",
            details=f"Found {len(time_slots)} optimal time slots"
        )
        
        return {
            "platform": platform,
            "content_type": content_type,
            "is_weekend": is_weekend,
            "optimal_times": time_slots,
            "best_time": time_slots[0]
        }

class PlatformTimeProfileTool(BaseTool):
    """Tool for analyzing platform-specific timing characteristics"""
    
    name: str = "Platform Time Profiler"
    description: str = "Provides platform-specific timing recommendations based on audience activity"
    
    def _run(self, platform: str, target_audience: str = "general") -> Dict[str, Any]:
        """Get platform-specific timing recommendations"""
        logger.info(f"Getting time profile for {platform}, audience: {target_audience}")
        
        # Mock implementation - would use real data in production
        platform_profiles = {
            "twitter": {
                "general": {"peak_hours": ["07:00-09:00", "12:00-14:00", "17:00-19:00"]},
                "professionals": {"peak_hours": ["07:00-08:00", "12:00-13:00", "17:00-18:00"]},
                "youth": {"peak_hours": ["15:00-17:00", "19:00-22:00"]},
            },
            "instagram": {
                "general": {"peak_hours": ["11:00-13:00", "19:00-21:00"]},
                "professionals": {"peak_hours": ["07:30-08:30", "18:00-19:30"]},
                "youth": {"peak_hours": ["15:00-17:00", "20:00-23:00"]},
            },
            # Add more platforms as needed
        }
        
        # Get platform profile or default to twitter
        platform_data = platform_profiles.get(platform.lower(), platform_profiles["twitter"])
        
        # Get audience profile or default to general
        audience_data = platform_data.get(target_audience, platform_data["general"])
        
        return {
            "platform": platform,
            "target_audience": target_audience,
            "peak_hours": audience_data["peak_hours"],
            "recommendation": audience_data["peak_hours"][0]
        }

class SocialPosterTool(BaseTool):
    """Base tool for posting to social media platforms"""
    
    name: str = "Social Poster"
    description: str = "Posts content to specified social media platform"
    
    def __init__(self, social_connector_manager: SocialConnectorManager):
        super().__init__()
        self.social_connector_manager = social_connector_manager
    
    def _run(self, platform: str, content: Dict[str, Any], schedule_time: Optional[str] = None) -> Dict[str, Any]:
        """Post content to the specified platform"""
        logger.info(f"Posting to {platform}")
        
        try:
            # Get the appropriate connector for the platform
            connector = self.social_connector_manager.get_connector(platform)
            
            if not connector:
                raise ValueError(f"No connector available for platform: {platform}")
            
            # Determine if this is an immediate post or scheduled
            if schedule_time:
                # Convert schedule_time string to datetime
                schedule_datetime = datetime.fromisoformat(schedule_time)
                result = connector.schedule_content(content, schedule_datetime.isoformat())
                status = "scheduled"
            else:
                result = connector.publish_content(content)
                status = "published"
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.CONTENT,
                message=f"Content {status} on {platform}",
                details=f"Content ID: {result.get('id', 'unknown')}"
            )
            
            return {
                "success": True,
                "platform": platform,
                "status": status,
                "post_id": result.get("id", ""),
                "url": result.get("url", ""),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error posting to {platform}: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.CONTENT,
                message=f"Failed to post to {platform}",
                details=str(e)
            )
            
            return {
                "success": False,
                "platform": platform,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

class SentimentTool(BaseTool):
    """Tool for analyzing sentiment of comments and messages"""
    
    name: str = "Sentiment Analyzer"
    description: str = "Analyzes sentiment of text content"
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        logger.info(f"Analyzing sentiment of text: {text[:50]}...")
        
        try:
            # Use AI provider to analyze sentiment
            prompt = f"""
            Analyze the sentiment of the following text and categorize it as:
            - positive
            - neutral
            - negative
            - very_negative
            
            Also provide a sentiment score from -1.0 (very negative) to 1.0 (very positive).
            
            Text: "{text}"
            
            Return the result as a JSON object with the following fields:
            - sentiment: the sentiment category
            - score: the sentiment score
            - contains_profanity: boolean indicating if the text contains profanity
            - contains_sensitive: boolean indicating if the text contains sensitive topics
            - key_emotions: array of primary emotions detected (e.g., "angry", "happy", "sad")
            
            Only return the JSON object, nothing else.
            """
            
            messages = [
                {"role": "system", "content": "You are a sentiment analysis expert."},
                {"role": "user", "content": prompt}
            ]
            
            # Use provider manager to select appropriate provider
            context = {"task_type": "text_generation"}
            
            # Execute with failover
            provider = self.ai_provider_manager.select_provider(context)
            response = provider.chat_with_gpt(messages, model="gpt-3.5-turbo", max_tokens=200)
            
            result_text = response.get("text", "")
            
            # Extract JSON from response
            import json
            import re
            
            # Try to find JSON in the response
            json_match = re.search(r'(\{.*\})', result_text, re.DOTALL)
            if json_match:
                result_json = json.loads(json_match.group(1))
            else:
                # Fallback to default values
                result_json = {
                    "sentiment": "neutral",
                    "score": 0.0,
                    "contains_profanity": False,
                    "contains_sensitive": False,
                    "key_emotions": ["neutral"]
                }
            
            log_event(
                level=LogLevel.INFO,
                category=LogCategory.ANALYTICS,
                message="Sentiment analysis completed",
                details=f"Sentiment: {result_json['sentiment']}, Score: {result_json['score']}"
            )
            
            return result_json
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.ANALYTICS,
                message="Sentiment analysis failed",
                details=str(e)
            )
            
            return {
                "sentiment": "neutral",
                "score": 0.0,
                "contains_profanity": False,
                "contains_sensitive": False,
                "key_emotions": ["neutral"],
                "error": str(e)
            }

class BrandToneGuideTool(BaseTool):
    """Tool for ensuring responses match brand tone and guidelines"""
    
    name: str = "Brand Tone Guide"
    description: str = "Ensures content matches brand voice and guidelines"
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, text: str, brand_guidelines: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and adjust text to match brand guidelines"""
        logger.info(f"Applying brand guidelines to text: {text[:50]}...")
        
        try:
            # Extract brand tone parameters
            tone = brand_guidelines.get("tone", "professional")
            voice = brand_guidelines.get("voice", "friendly")
            taboo_words = brand_guidelines.get("taboo_words", [])
            preferred_terms = brand_guidelines.get("preferred_terms", {})
            
            # Use AI provider to adjust text
            prompt = f"""
            Adjust the following text to match these brand guidelines:
            - Tone: {tone}
            - Voice: {voice}
            - Avoid these words/phrases: {', '.join(taboo_words)}
            - Use preferred terms: {json.dumps(preferred_terms)}
            
            Original text: "{text}"
            
            Return a JSON object with:
            - adjusted_text: the revised text
            - compliance_score: how well it matches guidelines (0-100)
            - changes: list of changes made
            
            Only return the JSON object, nothing else.
            """
            
            messages = [
                {"role": "system", "content": "You are a brand voice expert."},
                {"role": "user", "content": prompt}
            ]
            
            # Use provider manager to select appropriate provider
            context = {"task_type": "text_generation"}
            
            # Execute with failover
            provider = self.ai_provider_manager.select_provider(context)
            response = provider.chat_with_gpt(messages, model="gpt-3.5-turbo", max_tokens=300)
            
            result_text = response.get("text", "")
            
            # Extract JSON from response
            import json
            import re
            
            # Try to find JSON in the response
            json_match = re.search(r'(\{.*\})', result_text, re.DOTALL)
            if json_match:
                result_json = json.loads(json_match.group(1))
            else:
                # Fallback to original text
                result_json = {
                    "adjusted_text": text,
                    "compliance_score": 50,
                    "changes": []
                }
            
            return result_json
            
        except Exception as e:
            logger.error(f"Error applying brand guidelines: {str(e)}")
            return {
                "adjusted_text": text,
                "compliance_score": 0,
                "changes": [],
                "error": str(e)
            }

class KeywordFlaggerTool(BaseTool):
    """Tool for flagging keywords in text content"""
    
    name: str = "Keyword Flagger"
    description: str = "Flags sensitive or problematic keywords in text"
    
    def _run(self, text: str, keywords: List[str] = None) -> Dict[str, Any]:
        """Flag keywords in text"""
        logger.info(f"Flagging keywords in text: {text[:50]}...")
        
        # Default sensitive keywords if none provided
        if not keywords:
            keywords = [
                "complaint", "unhappy", "disappointed", "refund", "angry",
                "terrible", "worst", "hate", "lawsuit", "legal action"
            ]
        
        flagged_words = []
        for keyword in keywords:
            if keyword.lower() in text.lower():
                flagged_words.append(keyword)
        
        return {
            "flagged": len(flagged_words) > 0,
            "keywords": flagged_words,
            "severity": min(len(flagged_words) * 25, 100)  # 0-100 scale
        }

# =============== AGENTS ===============

class SchedulerAgent:
    """Agent for scheduling optimal posting times"""
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        """Initialize the scheduler agent"""
        self.ai_provider_manager = ai_provider_manager
        
        # Initialize tools
        self.engagement_tool = EngagementAnalyticsTool()
        self.platform_time_tool = PlatformTimeProfileTool()
        
        # Initialize CrewAI agent
        self.agent = Agent(
            role="Social Media Timing Strategist",
            goal="Determine the optimal posting time for maximum engagement",
            backstory="""You are an expert in social media analytics with years of 
            experience optimizing post timing across all major platforms. You understand 
            audience behavior patterns and how to maximize content visibility.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.engagement_tool, self.platform_time_tool]
        )
    
    def create_scheduling_task(self, content_metadata: Dict[str, Any]) -> Task:
        """Create a task for the scheduler agent"""
        platform = content_metadata.get("platform", "twitter")
        content_type = content_metadata.get("content_type", "general")
        target_audience = content_metadata.get("target_audience", "general")
        
        return Task(
            description=f"""
            Determine the optimal posting time for:
            - Platform: {platform}
            - Content type: {content_type}
            - Target audience: {target_audience}
            
            Analyze both historical engagement data and platform-specific timing profiles.
            Return a specific timestamp in ISO format for when this content should be posted.
            """,
            agent=self.agent,
            expected_output="ISO format timestamp for optimal posting time"
        )
    
    def get_optimal_time(self, content_metadata: Dict[str, Any]) -> str:
        """Get the optimal posting time for the given content"""
        # Create a crew with just this agent
        crew = Crew(
            agents=[self.agent],
            tasks=[self.create_scheduling_task(content_metadata)],
            verbose=True,
            process=Process.sequential
        )
        
        # Run the crew
        results = crew.kickoff()
        
        # Parse the result to get the timestamp
        timestamp = results[0] if results else datetime.now().isoformat()
        
        return timestamp

class PosterAgent:
    """Agent for posting content to social media platforms"""
    
    def __init__(self, ai_provider_manager: AIProviderManager, social_connector_manager: SocialConnectorManager):
        """Initialize the poster agent"""
        self.ai_provider_manager = ai_provider_manager
        self.social_connector_manager = social_connector_manager
        
        # Initialize tools
        self.poster_tool = SocialPosterTool(social_connector_manager)
        
        # Initialize CrewAI agent
        self.agent = Agent(
            role="Social Media Publisher",
            goal="Publish content to social media platforms accurately and reliably",
            backstory="""You are a professional social media manager responsible for 
            publishing content