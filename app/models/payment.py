from mongoengine import Document, ReferenceField, DecimalField, StringField, DateTimeField, EnumField
from datetime import datetime
from enum import Enum
from .user import User
from .subscription import Subscription

class PaymentStatus(str, Enum):
    SUCCESSFUL = "successful"
    FAILED = "failed"
    PENDING = "pending"

class Payment(Document):
    user = ReferenceField(User, required=True)
    subscription = ReferenceField(Subscription, required=True)
    amount = DecimalField(required=True, precision=2)
    currency = StringField(required=True, default="USD")
    payment_date = DateTimeField(required=True)
    status = EnumField(PaymentStatus, default=PaymentStatus.PENDING)
    transaction_reference = StringField()
    created_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'payments',
        'indexes': ['user', 'subscription', 'status', 'payment_date']
    }