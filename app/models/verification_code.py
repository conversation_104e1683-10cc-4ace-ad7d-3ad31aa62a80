from mongoengine import Document, <PERSON><PERSON>ield, ReferenceField, BooleanField, DateTimeField, EnumField
from datetime import datetime, timedelta
from enum import Enum
from .user import User

class VerificationPlatform(str, Enum):
    TELEGRAM = "telegram"
    WHATSAPP = "whatsapp"
    # Add other platforms as needed

class VerificationCode(Document):
    code = StringField(required=True)
    user = ReferenceField(User, required=True)
    platform = EnumField(VerificationPlatform, required=True)
    is_used = BooleanField(default=False)
    created_at = DateTimeField(default=datetime.utcnow)
    expires_at = DateTimeField(required=True)

    meta = {
        'collection': 'verification_codes',
        'indexes': [
            'code',
            'user',
            {'fields': ['code', 'platform'], 'unique': True}
        ]
    }

    @classmethod
    def generate_for_user(cls, user_id, platform, expiry_minutes=10):
        """Generate a unique verification code for a user"""
        import random
        import string
        
        # Generate a code with format romi_XXXXXX (6 digits)
        code = f"romi_{''.join(random.choices(string.digits, k=6))}"
        
        # Set expiration time
        expires_at = datetime.utcnow() + timedelta(minutes=expiry_minutes)
        
        # Create and save the verification code
        verification = cls(
            code=code,
            user=user_id,
            platform=platform,
            expires_at=expires_at
        )
        verification.save()
        
        return verification

    @classmethod
    def verify(cls, code, platform):
        """Verify a code and mark it as used if valid"""
        now = datetime.utcnow()
        verification = cls.objects(
            code=code,
            platform=platform,
            is_used=False,
            expires_at__gt=now
        ).first()
        
        if verification:
            verification.is_used = True
            verification.save()
            return verification
        
        return None