from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, EnumField, <PERSON><PERSON>ield
from datetime import datetime
from enum import Enum
from .user import User

class LogLevel(str, Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class LogCategory(str, Enum):
    SYSTEM = "system"
    USER = "user"
    AUTH = "auth"
    POST = "post"
    COMMENT = "comment"
    PAYMENT = "payment"
    NOTIFICATION = "notification"
    AGENT = "agent"
    ACCOUNT = "account"

class Log(Document):
    level = EnumField(LogLevel, required=True)
    category = EnumField(LogCategory, required=True)
    message = StringField(required=True)
    details = StringField()
    user = ReferenceField(User)  # Optional, for user-related logs
    source = StringField()  # Source of the log (e.g., module name, function)
    created_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'logs',
        'indexes': [
            'level', 
            'category', 
            'user',
            # Use a unique name for the TTL index to avoid conflicts
            {
                'fields': ['created_at'], 
                'name': 'created_at_ttl_index', 
                'expireAfterSeconds': 2592000  # 30 days TTL
            }
        ],
        'ordering': ['-created_at']
    }
