from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>Field, BooleanField, DateTimeField
from datetime import datetime
from .user import User

class CommentSetting(Document):
    """Settings for automatic replies to comments"""
    user = ReferenceField(User, required=True, unique=True)
    enabled = BooleanField(default=False)
    reply_to_all = BooleanField(default=False)
    reply_to_negative = BooleanField(default=True)
    reply_template = StringField(default="Thank you for your feedback. We appreciate your comments and will address your concerns.")
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'comment_settings',
        'indexes': ['user']
    }

    def __str__(self):
        return f"CommentSetting for {self.user.email}"