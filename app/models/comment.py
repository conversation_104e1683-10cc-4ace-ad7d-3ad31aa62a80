from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dict<PERSON><PERSON>
from .user import User
from .post import Post

class Comment(Document):
    user = ReferenceField(User, required=True)
    post = ReferenceField(Post, required=True)
    fb_comment_id = StringField(required=True)  # Facebook comment ID
    fb_post_id = StringField(required=True)  # Facebook post ID
    message = StringField(required=True)
    from_user = DictField(required=True)  # Facebook user info
    created_time = DateTimeField(required=True)
    is_replied = BooleanField(default=False)
    reply_message = StringField()
    reply_time = DateTimeField()
    sentiment = DictField()  # Stores sentiment analysis results
    is_negative = BooleanField(default=False)
    is_flagged = BooleanField(default=False)
    is_auto_replied = BooleanField(default=False)

    meta = {
        'collection': 'comments',
        'indexes': [
            'user',
            'post',
            'fb_comment_id',
            'fb_post_id',
            'created_time',
            'is_replied',
            'is_negative',
            'is_flagged'
        ],
        'ordering': ['-created_time']
    }

    def __str__(self):
        return f"Comment from {self.from_user.get('name', 'Unknown')} on {self.created_time}" 