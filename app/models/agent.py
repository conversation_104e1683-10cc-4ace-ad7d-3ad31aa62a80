from mongoengine import Document, ReferenceField, StringField, <PERSON>ctField, DateTimeField, EnumField, BooleanField
from datetime import datetime
from enum import Enum
from .user import User

class AgentType(str, Enum):
    CONTENT_CREATOR = "content_creator"
    AUTO_POSTER = "auto_poster"
    COMMENT_RESPONDER = "comment_responder"
    ANALYTICS = "analytics"

class Agent(Document):
    user = ReferenceField(User, required=True)
    name = StringField(required=True)
    description = StringField()
    agent_type = EnumField(AgentType, required=True)
    config = DictField(default={})
    status = BooleanField(default=True)  # True for active, False for inactive
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'agents',
        'indexes': ['user', 'agent_type', 'status']
    }