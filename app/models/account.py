
from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON><PERSON>, En<PERSON><PERSON><PERSON>
from datetime import datetime
from enum import Enum
from .user import User
from .agent import Agent

class Platform(str, Enum):
    FACEBOOK = "facebook"
    INSTAGRAM = "instagram"
    TWITTER = "twitter"
    LINKEDIN = "linkedin"
    TELEGRAM = "telegram"
    WHATSAPP = "whatsapp"

class AccountStatus(str, Enum):
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"

class Account(Document):
    user = ReferenceField(User, required=True)
    agent = ReferenceField(Agent)
    platform = EnumField(Platform, required=True)
    account_name = StringField(required=True)
    account_identifier = StringField(required=True)
    status = EnumField(AccountStatus, default=AccountStatus.DISCONNECTED)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'accounts',
        'indexes': ['user', 'platform', 'status']
    }
