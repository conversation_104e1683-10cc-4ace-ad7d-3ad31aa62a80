from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, DateTimeField, EnumField, Dict<PERSON>ield
from datetime import datetime
from enum import Enum
from .user import User
from .agent import Agent
from .account import Account  # Changed from .channel to .account

class PostStatus(str, Enum):
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    FAILED = "failed"

class Post(Document):
    user = ReferenceField(User, required=True)
    agent = ReferenceField(Agent)
    account = ReferenceField(Account)  # Changed from channel to account
    content = StringField(required=True)
    media_url = StringField()
    scheduled_at = DateTimeField()
    published_at = DateTimeField()
    status = EnumField(PostStatus, default=PostStatus.DRAFT)
    analytics_data = DictField(default={})
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'posts',
        'indexes': ['user', 'status', 'scheduled_at', 'published_at']
    }
