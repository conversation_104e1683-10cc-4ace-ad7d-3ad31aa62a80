from mongoengine import Document, ReferenceField, StringField, DateTimeField, EnumField, BooleanField
from datetime import datetime
from enum import Enum
from .user import User

class NotificationType(str, Enum):
    SYSTEM = "system"
    SUBSCRIPTION = "subscription"
    CONTENT = "content"

class Notification(Document):
    user = ReferenceField(User, required=True)
    title = StringField(required=True)
    body = StringField(required=True)
    notification_type = EnumField(NotificationType, required=True)
    is_read = BooleanField(default=False)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'notifications',
        'indexes': ['user', 'notification_type', 'is_read', 'created_at']
    }