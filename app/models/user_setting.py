from mongoengine import Document, <PERSON><PERSON>ield, <PERSON><PERSON>ield, Dict<PERSON><PERSON>, DateT<PERSON><PERSON>ield
from datetime import datetime
from .user import User

class UserSetting(Document):
    user = ReferenceField(User, required=True, unique=True)
    preferred_language = StringField(default='en')
    timezone = StringField(default='UTC')
    notification_preferences = DictField(default={
        'email': True,
        'sms': False,
        'in_app': True
    })
    post_format_preferences = DictField()
    personalization_profile = DictField(default={})  # New field for user content preferences
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'user_settings',
        'indexes': ['user']
    }
