# Rominext Manager Commands

## Setup Commands

### Initial Setup
```bash

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up .env file (create this file manually with the following content)
# FACEBOOK_PAGE_ACCESS_TOKEN=your_facebook_page_access_token
# FACEBOOK_PAGE_ID=your_facebook_page_id
# GROQ_API_KEY=your_groq_api_key
# MONGODB_URI=mongodb://localhost:27017/rominext
# SECRET_KEY=your_secret_key

# Initialize database
python run.py init-db

# Create admin user
python run.py create-admin
```

### Running the Application
```bash
# Start the application
python run.py run

automatically kill any processes using port 5000:
python run.py run --auto-kill

specify a different port:
python run.py run --port 8000 --auto-kill

# Run with Docker
docker-compose up -d
```

## Maintenance Commands

### Database Management
```bash
# Initialize/reset database
python run.py init-db

# Create admin user
python run.py create-admin --email <EMAIL> --password yourpassword
```

### System Checks
```bash
# Perform health check
python run.py health-check

# View logs
tail -f logs/rominext.log
```

## Development Commands

### Git Workflow
```bash
# Create new feature branch
git checkout -b feature-name

# Commit changes
git commit -m "Add feature-name"

# Push to remote
git push origin feature-name
```

# MongoDB Setup and Management

## Installing MongoDB on Ubuntu
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -

# Create list file for MongoDB
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list

# Update package list
sudo apt-get update

# Install MongoDB
sudo apt-get install -y mongodb-org
```

## Starting MongoDB Service
```bash
# Create data directory if it doesn't exist
sudo mkdir -p /data/db
sudo chown -R `id -un` /data/db

# Start MongoDB service
sudo service mongod start
```

## Verifying MongoDB Status
```bash
# Check if MongoDB is running
mongo --eval 'db.runCommand({ connectionStatus: 1 })'

# Check MongoDB service status
sudo systemctl status mongod
```

## MongoDB Management
```bash
# Start MongoDB
sudo systemctl start mongod

# Stop MongoDB
sudo systemctl stop mongod

# Restart MongoDB
sudo systemctl restart mongod

# Enable MongoDB to start on boot
sudo systemctl enable mongod




 already in use problem in flask
 lsof -i :5000
 kill -9 12345


install packages
pip install -r requirements.txt



debug mode :
python run.py run --debug

for swagger
http://localhost:5000/api/docs/

To get a token for authenticating with your API through Swagger UI, you need to use the Telegram Bot Token that's set in your environment variables.

python run.py run --debug --auto-kill




# Deactivate current environment
conda deactivate

# Remove and recreate the environment
conda remove --name rominext --all
conda create --name rominext python=3.11

# Activate the fresh environment
conda activate rominext

# Install packages from the updated requirements.txt
pip install -r requirements.txt

```




