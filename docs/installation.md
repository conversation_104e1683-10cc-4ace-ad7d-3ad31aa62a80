# Installation Guide

This guide provides detailed instructions for setting up Rominext on your local development environment or production server.

## Prerequisites

### System Requirements

- **Operating System**: Linux, macOS, or Windows 10/11
- **Python**: 3.10 or higher
- **MongoDB**: 5.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 2GB free space
- **Network**: Internet connection for API integrations

### Required Software

1. **Python 3.10+**
   ```bash
   # Check Python version
   python --version
   # or
   python3 --version
   ```

2. **MongoDB 5.0+**
   ```bash
   # Check MongoDB version
   mongod --version
   ```

3. **Git**
   ```bash
   # Check Git version
   git --version
   ```

4. **Node.js** (Optional, for frontend development)
   ```bash
   # Check Node.js version
   node --version
   ```

## Installation Methods

### Method 1: Local Development Setup

#### Step 1: Clone the Repository

```bash
git clone https://github.com/your-org/rominext.git
cd rominext
```

#### Step 2: Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate

# On Windows:
venv\Scripts\activate
```

#### Step 3: Install Dependencies

```bash
# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt
```

#### Step 4: Environment Configuration

1. **Create Environment File**
   ```bash
   cp .env.example .env
   ```

2. **Configure Environment Variables**
   Edit the `.env` file with your specific configuration:

   ```bash
   # Database Configuration
   MONGODB_URI=mongodb://localhost:27017/rominext
   MONGODB_DB=rominext
   MONGODB_HOST=localhost
   MONGODB_PORT=27017
   MONGODB_USERNAME=
   MONGODB_PASSWORD=

   # Security
   SECRET_KEY=your-super-secret-key-here-change-in-production

   # AI Provider API Keys
   OPENAI_API_KEY=your-openai-api-key
   DEEPSEEK_API_KEY=your-deepseek-api-key
   GROK_API_KEY=your-grok-api-key
   HUGGINGFACE_API_KEY=your-huggingface-api-key
   TOGETHER_API_KEY=your-together-api-key
   STABILITY_API_KEY=your-stability-api-key
   DEEPINFRA_API_KEY=your-deepinfra-api-key

   # Social Media API Keys
   FACEBOOK_PAGE_ACCESS_TOKEN=your-facebook-page-token
   FACEBOOK_PAGE_ID=your-facebook-page-id
   INSTAGRAM_ACCESS_TOKEN=your-instagram-token
   INSTAGRAM_ACCOUNT_ID=your-instagram-account-id
   TWITTER_BEARER_TOKEN=your-twitter-bearer-token
   LINKEDIN_ACCESS_TOKEN=your-linkedin-token

   # Telegram Bot
   TELEGRAM_BOT_TOKEN=your-telegram-bot-token

   # Application Settings
   FLASK_ENV=development
   FLASK_DEBUG=1
   DEFAULT_AI_PROVIDER=openai
   DEFAULT_SOCIAL_PLATFORM=facebook
   ```

#### Step 5: Database Setup

1. **Start MongoDB**
   ```bash
   # On Linux/macOS with systemd:
   sudo systemctl start mongod

   # On macOS with Homebrew:
   brew services start mongodb-community

   # On Windows:
   net start MongoDB
   ```

2. **Initialize Database**
   ```bash
   python run.py init-db
   ```

3. **Create Admin User**
   ```bash
   python run.py create-admin
   ```
   Follow the prompts to enter admin email and password.

#### Step 6: Verify Installation

```bash
# Run health check
python run.py health-check

# Start the application
python run.py run --debug
```

The application should be available at:
- **Web Interface**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs/

### Method 2: Docker Setup

#### Prerequisites for Docker

- Docker 20.10+
- Docker Compose 2.0+

#### Step 1: Clone Repository

```bash
git clone https://github.com/your-org/rominext.git
cd rominext
```

#### Step 2: Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Edit .env file with your API keys and configuration
```

#### Step 3: Build and Run

```bash
# Create Docker network
docker network create mongo-network

# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f
```

#### Step 4: Initialize Database (Docker)

```bash
# Initialize database
docker-compose exec app python run.py init-db

# Create admin user
docker-compose exec app python run.py create-admin
```

### Method 3: Production Deployment

#### Using Gunicorn

```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app

# Or with configuration file
gunicorn -c gunicorn.conf.py run:app
```

#### Using systemd (Linux)

1. **Create systemd service file**
   ```bash
   sudo nano /etc/systemd/system/rominext.service
   ```

2. **Service configuration**
   ```ini
   [Unit]
   Description=Rominext Social Media Management Platform
   After=network.target

   [Service]
   User=www-data
   Group=www-data
   WorkingDirectory=/path/to/rominext
   Environment=PATH=/path/to/rominext/venv/bin
   ExecStart=/path/to/rominext/venv/bin/gunicorn -w 4 -b 0.0.0.0:5000 run:app
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

3. **Enable and start service**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable rominext
   sudo systemctl start rominext
   ```

## Database Configuration

### MongoDB Setup

#### Local MongoDB Installation

**Ubuntu/Debian:**
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Update package database
sudo apt-get update

# Install MongoDB
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

**macOS:**
```bash
# Install using Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB
brew services start mongodb-community
```

**Windows:**
1. Download MongoDB Community Server from https://www.mongodb.com/try/download/community
2. Run the installer and follow the setup wizard
3. Start MongoDB service from Services panel

#### MongoDB Atlas (Cloud)

1. Create account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Configure network access and database user
4. Get connection string and update `MONGODB_URI` in `.env`

### Database Indexes

The application automatically creates necessary indexes, but you can manually create them:

```javascript
// Connect to MongoDB shell
use rominext

// Create indexes
db.users.createIndex({ "email": 1 }, { unique: true })
db.posts.createIndex({ "user": 1, "status": 1, "scheduled_at": 1 })
db.accounts.createIndex({ "user": 1, "platform": 1 })
db.analytics.createIndex({ "post": 1, "date": 1 })
```

## API Keys Setup

### AI Provider APIs

#### OpenAI
1. Visit https://platform.openai.com/api-keys
2. Create new API key
3. Add to `.env` as `OPENAI_API_KEY`

#### DeepSeek
1. Visit https://platform.deepseek.com/api-keys
2. Create new API key
3. Add to `.env` as `DEEPSEEK_API_KEY`

#### Grok (xAI)
1. Visit https://console.x.ai/
2. Create new API key
3. Add to `.env` as `GROK_API_KEY`

### Social Media APIs

#### Facebook/Instagram
1. Visit https://developers.facebook.com/
2. Create new app
3. Add Facebook Login and Instagram Basic Display products
4. Generate Page Access Token
5. Add tokens to `.env`

#### Twitter/X
1. Visit https://developer.twitter.com/
2. Create new app
3. Generate Bearer Token
4. Add to `.env` as `TWITTER_BEARER_TOKEN`

#### LinkedIn
1. Visit https://www.linkedin.com/developers/
2. Create new app
3. Request access to LinkedIn Pages API
4. Generate access token
5. Add to `.env` as `LINKEDIN_ACCESS_TOKEN`

### Telegram Bot

1. Message @BotFather on Telegram
2. Create new bot with `/newbot`
3. Get bot token
4. Add to `.env` as `TELEGRAM_BOT_TOKEN`

## Verification

### Health Check

```bash
# Run comprehensive health check
python run.py health-check
```

### Test API Endpoints

```bash
# Test with curl
curl -X GET http://localhost:5000/api/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

### Test Database Connection

```bash
# Test MongoDB connection
python -c "
from pymongo import MongoClient
client = MongoClient('mongodb://localhost:27017/')
print('MongoDB connection successful')
print('Databases:', client.list_database_names())
"
```

## Troubleshooting

### Common Issues

#### MongoDB Connection Failed
```bash
# Check if MongoDB is running
sudo systemctl status mongod

# Check MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Restart MongoDB
sudo systemctl restart mongod
```

#### Port Already in Use
```bash
# Find process using port 5000
lsof -i :5000

# Kill process
kill -9 <PID>

# Or use the built-in auto-kill feature
python run.py run --auto-kill
```

#### Missing Dependencies
```bash
# Reinstall requirements
pip install --force-reinstall -r requirements.txt

# Clear pip cache
pip cache purge
```

#### Permission Errors
```bash
# Fix file permissions
chmod +x run.py
chown -R $USER:$USER .

# Virtual environment permissions
chmod -R 755 venv/
```

### Environment Variables Not Loading

1. Check `.env` file exists in project root
2. Verify no spaces around `=` in `.env` file
3. Restart application after changing `.env`
4. Check for typos in variable names

### API Key Issues

1. Verify API keys are valid and not expired
2. Check API quotas and limits
3. Ensure proper permissions for social media APIs
4. Test API keys independently

## Next Steps

After successful installation:

1. **Configure AI Providers**: See [Configuration Guide](configuration.md)
2. **Set up Social Media Accounts**: See [User Guide](user-guide.md)
3. **Explore API**: Visit http://localhost:5000/api/docs/
4. **Run Tests**: See [Testing Guide](testing.md)
5. **Deploy to Production**: See [Deployment Guide](deployment.md)

## Support

If you encounter issues during installation:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review application logs in `logs/rominext.log`
3. Search existing issues on GitHub
4. Create a new issue with detailed error information

---

**Installation complete!** 🎉 Your Rominext instance should now be running and ready for configuration.
