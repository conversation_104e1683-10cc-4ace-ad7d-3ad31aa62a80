from flask import request, jsonify
from functools import wraps
import os
from app.services.vault_service import VaultService

# Load environment variables
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
INSTAGRAM_API_KEY = os.getenv("INSTAGRAM_API_KEY")

def authenticate_telegram(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get("Authorization")
        if not token or token.split(" ")[1] != TELEGRAM_BOT_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        return f(*args, **kwargs)
    return decorated_function

def authenticate_instagram(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get("Authorization")
        if not token or token.split(" ")[1] != INSTAGRAM_API_KEY:
            return jsonify({"error": "Unauthorized"}), 401
        return f(*args, **kwargs)
    return decorated_function
