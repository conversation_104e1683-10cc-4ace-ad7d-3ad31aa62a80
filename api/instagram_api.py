from flask import Flask, request, jsonify
from functools import wraps
import os
from ..app.services.account_service import AccountService
from .auth import authenticate_instagram as authenticate

app = Flask(__name__)

# Load environment variables
INSTAGRAM_API_KEY = os.getenv("INSTAGRAM_API_KEY")
INSTAGRAM_API_RATE_LIMIT = int(os.getenv("INSTAGRAM_API_RATE_LIMIT", 100))

# Rate limiting decorator (reuse from telegram_api or move to shared module)
def rate_limit(f):
    # Implementation...

@app.route('/api/instagram/some-endpoint', methods=['POST'])
@authenticate
@rate_limit
def some_endpoint():
    # Implementation...