# AI Provider Configuration
# Specifies which provider to use for different content types and tasks

# Content type configuration
content:
  text: grok
  image: stabilityai
  code: deepseek
  chat: deepseek
  
# Task type configuration
tasks:
  text_generation: grok
  image_generation: stabilityai
  chat: deepseek
  embeddings: openai
  code_generation: deepseek
  translation: grok
  summarization: deepseek
  image_to_image: stabilityai
  upscaling: stabilityai
  image_to_text: huggingface
  
# Model mappings by provider
models:
  llama:
    deepinfra: "meta-llama/Llama-2-70b-chat-hf"
    togetherai: "meta-llama/Llama-2-70B-Chat"
  
  mixtral:
    deepinfra: "mistralai/Mixtral-8x7B-Instruct-v0.1"
    togetherai: "mistralai/Mixtral-8x7B-Instruct-v0.1"
    
  stable_diffusion:
    deepinfra: "stability-ai/sdxl"
    stabilityai: "stable-diffusion-xl-1024-v1-0"


