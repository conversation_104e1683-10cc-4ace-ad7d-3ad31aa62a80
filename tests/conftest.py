import pytest
import sys
import os
from flask import Flask

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now try importing
from app import create_app
from app.config import TestConfig
from mongoengine import connect, disconnect
from app.models import User

@pytest.fixture
def app():
    """Create and configure a new app instance for each test."""
    app = create_app(TestConfig)
    yield app

@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """A test runner for the app's Click commands."""
    return app.test_cli_runner() 

@pytest.fixture(scope="function")
def db():
    """Set up and tear down the database for each test"""
    # Connect to a test database using mongomock
    connect('test_db', host='mongomock://localhost')
    
    yield
    
    # Clean up after the test
    disconnect()
