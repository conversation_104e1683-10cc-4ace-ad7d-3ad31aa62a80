# Test Suite Documentation

This directory contains the test suite for the Rominext application. The tests are organized into different categories:

- `models/`: Tests for database models
- `api/`: Tests for API endpoints
- `services/`: Tests for service layer
- `utils/`: Tests for utility functions
- `blueprints/`: Tests for Flask blueprints

## Prerequisites

1. Install the test dependencies:
```bash
pip install -r requirements-test.txt
```

2. Make sure you have MongoDB running locally or configure the test database in `tests/config.py`

## Running Tests

To run all tests:
```bash
pytest
```

To run tests with coverage report:
```bash
pytest --cov=app --cov-report=term-missing --cov-report=html
```

To run specific test files:
```bash
pytest tests/models/test_user.py
```

To run tests with verbose output:
```bash
pytest -v
```

## Test Structure

Each test file follows this general structure:
1. Fixtures for test data setup
2. Test cases organized by functionality
3. Assertions to verify expected behavior

## Adding New Tests

When adding new tests:
1. Create a new test file in the appropriate directory
2. Follow the naming convention: `test_*.py`
3. Use fixtures for common setup
4. Include docstrings explaining the test purpose
5. Add both positive and negative test cases

## Test Coverage

The test suite aims to cover:
- All database models
- API endpoints
- Business logic in services
- Utility functions
- Error handling
- Edge cases

## Continuous Integration

The test suite is integrated with the CI pipeline and runs automatically on:
- Pull requests
- Merges to main branch
- Scheduled runs 