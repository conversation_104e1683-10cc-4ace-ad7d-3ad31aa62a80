import pytest
from app.models import User, User<PERSON><PERSON>, UserStatus
from mongoengine import ValidationError

class TestUserModel:
    def test_create_user(self):
        """Test creating a user with the factory method"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.role == UserRole.CUSTOMER
        assert user.status == UserStatus.ACTIVE
        assert user.check_password("password123")
        
    def test_password_hashing(self):
        """Test password hashing functionality"""
        user = User(email="<EMAIL>", name="Test User")
        user.set_password("password123")
        
        assert user.password_hash != "password123"
        assert user.check_password("password123")
        assert not user.check_password("wrongpassword")
    
    def test_user_validation(self):
        """Test user model validation"""
        # Missing required field
        user = User(name="Test User")
        with pytest.raises(ValidationError):
            user.save()