import pytest
from app.models import UserSetting, User

class TestUserSettingModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_user_setting(self, user):
        """Test creating user settings"""
        settings = UserSetting(
            user=user,
            preferred_language="en",
            timezone="America/New_York",
            notification_preferences={
                "email": True,
                "sms": False,
                "in_app": True
            },
            personalization_profile={
                "interests": ["technology", "marketing"],
                "content_style": "casual"
            }
        )
        settings.save()
        
        assert settings.user == user
        assert