import pytest
from datetime import datetime, timedelta
from app.models import ContentExperiment, User, Post, PostStatus

class TestContentExperimentModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    @pytest.fixture
    def posts(self, user):
        """Create test posts"""
        post_a = Post(
            user=user,
            content="Test post A content",
            status=PostStatus.PUBLISHED
        )
        post_a.save()
        
        post_b = Post(
            user=user,
            content="Test post B content",
            status=PostStatus.PUBLISHED
        )
        post_b.save()
        
        yield (post_a, post_b)
        
        post_a.delete()
        post_b.delete()
    
    def test_create_experiment(self, user, posts):
        """Test creating a content experiment"""
        post_a, post_b = posts
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=7)
        
        experiment = ContentExperiment(
            user=user,
            post_a=post_a,
            post_b=post_b,
            start_date=start_date,
            end_date=end_date,
            analytics_data={
                "post_a_impressions": 0,
                "post_b_impressions": 0,
                "post_a_engagements": 0,
                "post_b_engagements": 0
            }
        )
        experiment.save()
        
        assert experiment.user == user
        assert experiment.post_a == post_a
        assert experiment.post_b == post_b
        assert experiment.start_date == start_date
        assert experiment.end_date == end_date
        assert experiment.winner_post is None
    
    def test_set_winner(self, user, posts):
        """Test setting a winner for an experiment"""
        post_a, post_b = posts
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=7)
        
        experiment = ContentExperiment(
            user=user,
            post_a=post_a,
            post_b=post_b,
            start_date=start_date,
            end_date=end_date
        )
        experiment.save()
        
        # Set post_a as winner
        experiment.winner_post = post_a
        experiment.save()
        
        assert experiment.winner_post == post_a