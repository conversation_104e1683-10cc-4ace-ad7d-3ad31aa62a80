import pytest
from app.models import Strategy, User

class TestStrategyModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_strategy(self, user):
        """Test creating a strategy"""
        strategy = Strategy(
            user=user,
            name="Content Strategy 2023",
            description="Strategy for Q3 and Q4",
            business_type="SaaS",
            target_audience=["Developers", "IT Managers"],
            key_objectives=["Increase brand awareness", "Generate leads"],
            tone_of_voice="professional",
            key_topics=["AI", "Automation"],
            content_pillars=["Education", "Case Studies"],
            value_propositions=["Save time", "Reduce costs"]
        )
        strategy.save()
        
        assert strategy.user == user
        assert strategy.name == "Content Strategy 2023"
        assert "Developers" in strategy.target_audience
        assert strategy.is_active is True