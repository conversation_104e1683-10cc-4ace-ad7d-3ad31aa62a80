import pytest
from datetime import datetime
from app.models import Comment, User, Post, PostStatus

class TestCommentModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    @pytest.fixture
    def post(self, user):
        """Create a test post"""
        post = Post(
            user=user,
            content="Test post content",
            status=PostStatus.PUBLISHED
        )
        post.save()
        
        yield post
        
        post.delete()
    
    def test_create_comment(self, user, post):
        """Test creating a comment"""
        comment = Comment(
            user=user,
            post=post,
            content="This is a test comment",
            author_name="<PERSON>",
            platform_id="comment_123456",
            metadata={
                "likes": 5,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        comment.save()
        
        assert comment.user == user
        assert comment.post == post
        assert comment.content == "This is a test comment"
        assert comment.author_name == "<PERSON>"
        assert comment.platform_id == "comment_123456"
        assert comment.metadata.get("likes") == 5
        assert comment.created_at is not None
    
    def test_comment_reply(self, user, post):
        """Test comment reply functionality"""
        # Parent comment
        parent = Comment(
            user=user,
            post=post,
            content="Parent comment",
            author_name="John Doe",
            platform_id="comment_123"
        )
        parent.save()
        
        # Reply comment
        reply = Comment(
            user=user,
            post=post,
            content="Reply comment",
            author_name="Jane Smith",
            platform_id="comment_456",
            parent_comment=parent
        )
        reply.save()
        
        assert reply.parent_comment == parent
        assert reply.content == "Reply comment"