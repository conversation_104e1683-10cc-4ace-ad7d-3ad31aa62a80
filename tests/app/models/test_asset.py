import pytest
from app.models import Asset, User, AssetType

class TestAssetModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_asset(self, user):
        """Test creating an asset"""
        asset = Asset(
            user=user,
            file_url="https://example.com/image.jpg",
            file_type=AssetType.IMAGE,
            metadata={"width": 800, "height": 600}
        )
        asset.save()
        
        assert asset.user == user
        assert asset.file_url == "https://example.com/image.jpg"
        assert asset.file_type == AssetType.IMAGE
        assert asset.metadata.get("width") == 800
        assert asset.created_at is not None
    
    def test_asset_types(self, user):
        """Test different asset types"""
        # Test image asset
        image = Asset(user=user, file_url="https://example.com/image.jpg", file_type=AssetType.IMAGE)
        image.save()
        
        # Test video asset
        video = Asset(user=user, file_url="https://example.com/video.mp4", file_type=AssetType.VIDEO)
        video.save()
        
        # Test document asset
        document = Asset(user=user, file_url="https://example.com/doc.pdf", file_type=AssetType.DOCUMENT)
        document.save()
        
        # Test audio asset
        audio = Asset(user=user, file_url="https://example.com/audio.mp3", file_type=AssetType.AUDIO)
        audio.save()
        
        assert image.file_type == AssetType.IMAGE
        assert video.file_type == AssetType.VIDEO
        assert document.file_type == AssetType.DOCUMENT
        assert audio.file_type == AssetType.AUDIO