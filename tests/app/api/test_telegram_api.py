import pytest
import json
from unittest.mock import patch, MagicMock
from app.models import Account, Platform, AccountStatus

class TestTelegramAPI:
    @pytest.fixture
    def auth_headers(self):
        """Create authentication headers for API requests"""
        return {'X-Telegram-Auth': 'test-webhook-secret'}
    
    @patch('api.telegram_api.AccountService')
    def test_verify_code_success(self, mock_account_service, client, auth_headers):
        """Test verifying a Telegram code successfully"""
        # Mock the service response
        mock_account_service.verify_platform_code.return_value = (True, {"account_id": "123"})
        
        # Test data
        verify_data = {
            "telegram_user_id": "12345",
            "code": "romi_123456"
        }
        
        response = client.post(
            '/api/telegram/verify-code',
            data=json.dumps(verify_data),
            content_type='application/json',
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "Verification successful" in data['message']
        assert "account_id" in data['data']
        
        # Verify service was called correctly
        mock_account_service.verify_platform_code.assert_called_once_with(
            code="romi_123456",
            platform="telegram",
            platform_user_id="12345"
        )
    
    @patch('api.telegram_api.AccountService')
    def test_verify_code_failure(self, mock_account_service, client, auth_headers):
        """Test verifying an invalid Telegram code"""
        # Mock the service response for failure
        mock_account_service.verify_platform_code.return_value = (False, "Invalid code")
        
        # Test data
        verify_data = {
            "telegram_user_id": "12345",
            "code": "romi_123456"
        }
        
        response = client.post(
            '/api/telegram/verify-code',
            data=json.dumps(verify_data),
            content_type='application/json',
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "Verification failed" in data['error']
        assert "Invalid code" in data['message']
    
    def test_verify_code_missing_params(self, client, auth_headers):
        """Test verifying with missing parameters"""
        # Test data with missing code
        verify_data = {
            "telegram_user_id": "12345"
            # Missing code
        }
        
        response = client.post(
            '/api/telegram/verify-code',
            data=json.dumps(verify_data),
            content_type='application/json',
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "Missing required parameters" in data['error']
    
    @patch('api.telegram_api.Account')
    @patch('api.telegram_api.AccountService.get_account_by_platform_id')
    def test_connect_channel_success(self, mock_get_account, mock_account_model, client, auth_headers):
        """Test connecting a Telegram channel successfully"""
        # Mock user account
        user_account = MagicMock()
        user_account.user = MagicMock()
        mock_get_account.return_value = user_account
        
        # Mock the new channel account
        channel_account = MagicMock()
        mock_account_model.return_value = channel_account
        
        # Test data
        channel_data = {
            "telegram_user_id": "12345",
            "channel_id": "67890",
            "channel_title": "Test Channel"
        }
        
        response = client.post(
            '/api/telegram/connect-channel',
            data=json.dumps(channel_data),
            content_type='application/json',
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "Channel connected successfully" in data['message']
        
        # Verify account was created correctly
        mock_account_model.assert_called_once()
        channel_account.save.assert_called_once()
    
    @patch('api.telegram_api.AccountService.get_account_by_platform_id')
    def test_connect_channel_user_not_found(self, mock_get_account, client, auth_headers):
        """Test connecting a channel with unverified user"""
        # Mock user account not found
        mock_get_account.return_value = None
        
        # Test data
        channel_data = {
            "telegram_user_id": "12345",
            "channel_id": "67890"
        }
        
        response = client.post(
            '/api/telegram/connect-channel',
            data=json.dumps(channel_data),
            content_type='application/json',
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "User not verified" in data['error']