import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from telegram import Update, User, Chat, Message
from telegram.ext import ContextTypes
import sys
import os

# Add the project root to path if needed
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import bot.telegram.telegram_bot as telegram_bot

class TestTelegramBot:
    @pytest.fixture
    def mock_update(self):
        """Create a mock Update object"""
        update = MagicMock(spec=Update)
        update.effective_user = MagicMock(spec=User)
        update.effective_user.id = 12345
        update.message = MagicMock(spec=Message)
        update.message.reply_text = AsyncMock()
        update.message.text = ""
        return update
    
    @pytest.fixture
    def mock_context(self):
        """Create a mock Context object"""
        context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        context.args = []
        return context
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_start_command(self, mock_post, mock_update, mock_context):
        """Test the start command handler"""
        await telegram_bot.start(mock_update, mock_context)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Welcome to Rominext Bot" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_verify_command_valid(self, mock_post, mock_update, mock_context):
        """Test the verify command with valid code"""
        mock_context.args = ["romi_123456"]
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        await telegram_bot.verify_command(mock_update, mock_context)
        
        mock_post.assert_called_once_with(
            f"{telegram_bot.BACKEND_URL}/verify-code", 
            json={
                "telegram_user_id": mock_update.effective_user.id,
                "code": "romi_123456"
            }
        )
        mock_update.message.reply_text.assert_called_once()
        assert "Verification successful" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_verify_command_invalid_format(self, mock_post, mock_update, mock_context):
        """Test the verify command with invalid code format"""
        mock_context.args = ["invalid_code"]
        
        await telegram_bot.verify_command(mock_update, mock_context)
        
        mock_post.assert_not_called()
        mock_update.message.reply_text.assert_called_once()
        assert "valid verification code" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_verify_command_no_args(self, mock_post, mock_update, mock_context):
        """Test the verify command with no arguments"""
        mock_context.args = []
        
        await telegram_bot.verify_command(mock_update, mock_context)
        
        mock_post.assert_not_called()
        mock_update.message.reply_text.assert_called_once()
        assert "Please provide a verification code" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_handle_verification_valid(self, mock_post, mock_update, mock_context):
        """Test handling valid verification code message"""
        mock_update.message.text = "romi_123456"
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        await telegram_bot.handle_verification(mock_update, mock_context)
        
        mock_post.assert_called_once()
        mock_update.message.reply_text.assert_called_once()
        assert "Verification successful" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_handle_forwarded_message(self, mock_post, mock_update, mock_context):
        """Test handling forwarded message from channel"""
        # Create a mock for the forward information
        mock_update.message.forward_from_chat = MagicMock()
        mock_update.message.forward_from_chat.id = 67890
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        await telegram_bot.handle_forwarded(mock_update, mock_context)
        
        mock_post.assert_called_once_with(
            f"{telegram_bot.BACKEND_URL}/connect-channel", 
            json={
                "telegram_user_id": mock_update.effective_user.id,
                "channel_id": 67890
            }
        )
        mock_update.message.reply_text.assert_called_once()
        assert "Channel connected successfully" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_handle_forwarded_not_from_channel(self, mock_post, mock_update, mock_context):
        """Test handling message that is not forwarded from channel"""
        mock_update.message.forward_from_chat = None
        
        await telegram_bot.handle_forwarded(mock_update, mock_context)
        
        mock_post.assert_not_called()
        mock_update.message.reply_text.assert_called_once()
        assert "Please forward a message from your channel" in mock_update.message.reply_text.call_args[0][0]
    
    @patch('bot.telegram.telegram_bot.requests.post')
    async def test_handle_verification_api_error(self, mock_post, mock_update, mock_context):
        """Test handling API error during verification"""
        mock_update.message.text = "romi_123456"
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {"error": "Invalid code"}
        mock_post.return_value = mock_response
        
        await telegram_bot.handle_verification(mock_update, mock_context)
        
        mock_post.assert_called_once()
        mock_update.message.reply_text.assert_called_once()
        assert "Invalid code" in mock_update.message.reply_text.call_args[0][0]


