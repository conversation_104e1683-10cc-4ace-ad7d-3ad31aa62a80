Telegram Bot Connection Guide for Rominext
Steps to Connect Telegram Bot:
Create Your Telegram Bot:

Use @BotFather on Telegram to create a bot.

Save the API Token you get from BotFather.

Add the Bot to Your Channel:

Go to your Telegram channel or group.

Add @rominext_bot as an admin and grant it permission to post messages.

Forward a Message from Your Channel:

Forward any message from your channel to @rominext_bot (this will allow us to detect your channel).

Connect Your Channel to Rominext:

After forwarding the message, Rominext will automatically detect the channel.

You will see your channel appear as connected in the Rominext dashboard.

Important Information for Security:
🔒 Your Data Is Safe

Rominext uses Telegram's official Bot API. We do not store your password or access your personal content.

You can revoke access by removing @rominext_bot from your channel at any time.

For Multiple Channels:
If you wish to connect more than one channel, repeat the process for each channel. Each will appear individually on your Rominext dashboard.




Telegram Bot Hosting Guide for Rominext
Step 1: Choose a Hosting Platform
To run your Telegram bot in production, you need to choose a hosting platform. Common options include:

Railway (easy setup, good for smaller projects)

Heroku (easy and popular, but with limitations)

AWS EC2 or Google Cloud (for more control and scalability)

DigitalOcean (simple and cost-effective for bot hosting)

Step 2: Set Up the Environment
Install Dependencies:

Install Python (recommended version 3.8 or higher).

Use a virtual environment for Python dependencies.

Set Environment Variables:

Set your Telegram Bot Token as an environment variable (TELEGRAM_BOT_TOKEN).

In most hosting platforms, you can set this in the settings panel.

Step 3: Deploying the Bot
Using Railway/Heroku:

Create a new project, link it to your GitHub repository, and deploy your bot.

The platform will automatically install dependencies from your requirements.txt file.

Set up the environment variable (e.g., TELEGRAM_BOT_TOKEN) in the platform’s settings.

Using VPS (AWS, DigitalOcean, etc.):

SSH into your server.

Clone your repository, install Python dependencies, and run the bot.

For production, it's better to use a process manager like pm2 to keep the bot running.

Step 4: Running the Bot
For Hosting:

Use a web server like Nginx or directly run your bot script via a process manager (systemd, supervisord, pm2).

Webhooks (Optional for Production):

Set up webhooks instead of polling to improve performance and reliability.

Your server should be accessible via HTTPS (required for webhooks).

Step 5: Monitor & Maintain
Regularly check your bot’s performance (logs, message handling, etc.).

Set up error monitoring to ensure that your bot works smoothly in production.

Security & Privacy Considerations:
Keep your API token safe: Never expose it in the code.

Use HTTPS: Ensure your server supports HTTPS to secure communication.

Data privacy: Follow best practices to ensure users' data remains safe and private.





+---------------------+               +--------------------------+
|     Telegram Bot    | — HTTP POST → | rominext.com/api/telegram|
| (Docker container)  |               | (Flask app running @5000)|
+---------------------+               +--------------------------+
             |                                        ^
             | BACKEND_URL = http://host.docker.internal:5000 or http://127.0.0.1:5000 (for internal use)
             |
             +–––> NOT exposed via web – internal only





Commands:
running the bot as a module for local development: python -m bot.telegram.telegram_bot

