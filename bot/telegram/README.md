# Rominext Telegram Bot

## Running with Docker

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Telegram <PERSON> (from BotFather)

### Setup

1. Create a `.env` file in the `bot/telegram` directory with the following content:
```
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
BACKEND_URL=https://your-backend-url.com/api/telegram
```

2. Build and start the container:
```bash
cd bot/telegram
docker-compose up -d
```

3. Check the logs:
```bash
docker logs rominext_telegram_bot
```

### Stopping the Bot
```bash
docker-compose down
```

## Running Locally

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
export TELEGRAM_BOT_TOKEN=your_telegram_bot_token
export BACKEND_URL=https://your-backend-url.com/api/telegram
```

3. Run the bot:
```bash
python telegram_bot.py
```